__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

.env
.idea/
*.service

http-client.private.env.json

127.0.0.1.pem
127.0.0.1-key.pem

resources/poster_cache
deployment
prowlarr-config/
config/
mediafusion.local*
.scrapy
.lock
.git
kodi/
!kodi/*.py
!deployment/startup.sh

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/