name: MediaFusion CI/CD

on:
  release:
    types: [ created ]

jobs:
  update_version:
    runs-on: ubuntu-latest
    outputs:
      previous_version: ${{ steps.extract_versions.outputs.PREVIOUS_VERSION }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Extract versions
        id: extract_versions
        run: |
          # Extract current version from release body
          BODY="${{ github.event.release.body }}"
          if ! PREVIOUS_VERSION=$(echo "$BODY" | grep -o '/compare/.*\.\.\.' | sed 's/\/compare\///' | sed 's/\.\.\.//' || true); then
            echo "Error: Failed to extract previous version from release body"
            exit 1
          fi
          echo "PREVIOUS_VERSION=${PREVIOUS_VERSION}" >> "$GITHUB_OUTPUT"

      - name: Update version numbers
        if: "!github.event.release.prerelease"
        run: |
          make update-version VERSION_NEW=${{  github.ref_name }}

      - name: Commit and push version updates
        if: "!github.event.release.prerelease"
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add -A
          if git diff-index --quiet HEAD; then
            echo "No changes to commit"
            exit 0
          fi
          git commit -m "chore: update version to ${{ github.ref_name }}"
          git push origin HEAD:main

  mediafusion_docker_build:
    needs: update_version
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: main  # Use main branch with updated versions

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./deployment/Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          build-args: VERSION=${{ github.ref_name }}
          tags: |
            mhdzumair/mediafusion:${{ github.ref_name }}
            mhdzumair/mediafusion:${{ github.event.release.prerelease && 'beta' || 'latest' }}

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}

  kodi_build:
    needs: update_version
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: main  # Use main branch with updated versions

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.8'

      - name: Install Python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install setuptools wheel

      - name: Install required packages
        run: sudo apt-get install -y zip xmlstarlet

      - name: Build addon and repository
        run: |
          make -C kodi
          # Validate build artifacts
          for file in kodi/dist/plugin.video.mediafusion/plugin.video.mediafusion-*.zip kodi/dist/repository.mediafusion/repository.mediafusion-*.zip; do
            if [ ! -f "$file" ]; then
              echo "Error: Build artifact $file not found"
              exit 1
            fi
          done

      - name: Deploy to GitHub Pages
        if: "!github.event.release.prerelease"
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: kodi/dist
          enable_jekyll: false
          force_orphan: true

      - name: Upload Release Assets
        uses: softprops/action-gh-release@v2
        with:
          files: |
            kodi/dist/plugin.video.mediafusion/plugin.video.mediafusion-*.zip
          token: ${{ secrets.GITHUB_TOKEN }}

  generate_release_notes:
    needs: update_version
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install jq
        run: sudo apt-get install -y jq

      - name: Generate Release Notes
        run: |
          make generate-notes VERSION_OLD=${{ needs.update_version.outputs.previous_version }} VERSION_NEW=${{ github.ref_name }} ANTHROPIC_API_KEY=${{ secrets.ANTHROPIC_API_KEY }} > release_notes.md

      - name: Update Release Notes
        uses: softprops/action-gh-release@v2
        with:
          body_path: release_notes.md
          token: ${{ secrets.GITHUB_TOKEN }}