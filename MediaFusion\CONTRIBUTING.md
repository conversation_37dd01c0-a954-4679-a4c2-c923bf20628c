# Contributing to Media Fusion

First off, thank you for considering contributing to Media Fusion! We appreciate your effort and contribution. This document provides guidelines and steps for contributing.

## Code of Conduct

By participating, you are expected to uphold this code. Please report unacceptable behavior.

## How Can I Contribute?

### Reporting Bugs

- Ensure the bug was not already reported by searching on GitHub.
- If you're unable to find an open issue addressing the problem, open a new one. Be sure to include a title, clear description, and the necessary information to reproduce the bug.

### Suggesting Enhancements

- Open a new issue with a clear title and detailed description.
- Provide a step-by-step description of the suggested enhancement.

### Pull Requests

1. Fork the repo.
2. Create your feature branch: `git checkout -b my-new-feature`.
3. Commit your changes: `git commit -am 'Add some feature'`.
4. Push to the branch: `git push origin my-new-feature`.
5. Submit a pull request.

## Development Setup

1. Clone the repository.
2. Install dependencies using `pipenv install`.
3. Follow the instructions in the `README.md` to set up the environment variables.

## Style Guide

Ensure your code adheres to the PEP8 style guide for Python and use `black` to format your code.

## Commit Messages

- Use the present tense ("Add feature" not "Added feature").
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...").

## Additional Notes

- Ensure your PRs are small, focused, and solve only one issue or add one feature.
- Include comments in your code where necessary.

Thank you for your contribution!

