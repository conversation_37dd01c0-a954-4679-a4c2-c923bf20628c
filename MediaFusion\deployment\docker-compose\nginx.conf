user  nginx;
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    server {
        listen 443 ssl;
        server_name mediafusion.local;

        ssl_certificate     /etc/ssl/certs/mediafusion.local.pem;
        ssl_certificate_key /etc/ssl/private/mediafusion.local-key.pem;

        ssl_session_cache   shared:SSL:10m;
        ssl_session_timeout 10m;

        location / {
            proxy_pass http://mediafusion:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
