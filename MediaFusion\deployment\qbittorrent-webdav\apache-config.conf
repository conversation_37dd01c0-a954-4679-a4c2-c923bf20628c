<VirtualHost *:80>
    # Redirect /qbittorrent to /qbittorrent/ to ensure relative URLs resolve correctly
    RewriteEngine On
    RewriteRule ^/qbittorrent$ /qbittorrent/ [R=301,L]

    # Reverse proxy for qBittorrent WebUI
    ProxyPass /qbittorrent/ http://localhost:8080/
    ProxyPassReverse /qbittorrent/ http://localhost:8080/

    # WebDAV setup for qBittorrent downloads directory
    Alias /webdav /downloads
    <Directory /downloads>
        DAV On
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        # The authentication directives will be added here by the entrypoint script
    </Directory>
</VirtualHost>