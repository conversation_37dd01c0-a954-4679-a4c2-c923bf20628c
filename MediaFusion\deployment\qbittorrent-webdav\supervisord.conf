[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor
user=root

[program:apache2]
command=httpd -D FOREGROUND
user=qbtUser
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:qbittorrent-nox]
command=qbittorrent-nox --webui-port=%(ENV_QBT_WEBUI_PORT)s --profile=%(ENV_PROFILE_PATH)s
user=qbtUser
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
