{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyP/3jAD0HrOSOZAuPRU40e/", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"f94ff181953845f6bf1cd2790c06482c": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_5a643171d7704209ae26d0cc72f35de6", "IPY_MODEL_650a8365a5ea46f9897444f9d29976e3", "IPY_MODEL_1dd1de36da7147ebaf57c3ac26e01e16", "IPY_MODEL_ec31953cf7a54132b4b0a0e6492b0a9c", "IPY_MODEL_3dd745bd3811406e989f7e864ca5a64b", "IPY_MODEL_508b2c8e88a846978796cfdabb21cf30", "IPY_MODEL_cd1eeaec65554cb6b410a3403683103e", "IPY_MODEL_01357233b8444261999638b2821a143b", "IPY_MODEL_6d53d0d9969a4fc7b7711801fb71ba31", "IPY_MODEL_0c17e545386f44ef91972dab303008d8"], "layout": "IPY_MODEL_e09a60b3518145f4aa8048dcfbb9d10c"}}, "5a643171d7704209ae26d0cc72f35de6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4c1e8353eda0441b932db04f94badd3b", "placeholder": "​", "style": "IPY_MODEL_00ac08cdb7b34dc3b28c815e30e8ec95", "value": "<h3>WebSeed Torrent Creator</h3>"}}, "650a8365a5ea46f9897444f9d29976e3": {"model_module": "@jupyter-widgets/controls", "model_name": "TextModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "TextModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "TextView", "continuous_update": true, "description": "URL:", "description_tooltip": null, "disabled": false, "layout": "IPY_MODEL_81bbc52a95084b018dfe70b008bbd7fd", "placeholder": "Enter URL", "style": "IPY_MODEL_663594b1d1ea43f38a4311fd3a0c258d", "value": ""}}, "1dd1de36da7147ebaf57c3ac26e01e16": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsModel", "_options_labels": ["Direct Filename", "Use Metadata"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ToggleButtonsView", "button_style": "", "description": "Naming Mode:", "description_tooltip": null, "disabled": false, "icons": [], "index": 0, "layout": "IPY_MODEL_4a6dfa1b025e461a97f9dc1b9bcde46d", "style": "IPY_MODEL_3777b719f6a24a6389b65e4dea9300a7", "tooltips": []}}, "ec31953cf7a54132b4b0a0e6492b0a9c": {"model_module": "@jupyter-widgets/controls", "model_name": "TextModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "TextModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "TextView", "continuous_update": true, "description": "Filename:", "description_tooltip": null, "disabled": false, "layout": "IPY_MODEL_2e481d00aa3f427c8556703bbd070a1f", "placeholder": "Enter complete filename (e.g., Movie.Name.2024.1080p.BluRay.x264)", "style": "IPY_MODEL_1c260f8f16514b8da6f65b6285ef50e9", "value": ""}}, "3dd745bd3811406e989f7e864ca5a64b": {"model_module": "@jupyter-widgets/controls", "model_name": "DropdownModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DropdownModel", "_options_labels": ["mp4", "mkv", "avi", "webm"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "DropdownView", "description": "File Type:", "description_tooltip": null, "disabled": false, "index": 1, "layout": "IPY_MODEL_acb195c4fc23429cbc02f74ffa96f54d", "style": "IPY_MODEL_d490a1616e1549ea9fb34f35b485e49b"}}, "508b2c8e88a846978796cfdabb21cf30": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_7ccdf13407dc4dabba45164021d0073c", "IPY_MODEL_4c8961f45fd14de492894bcd29e8c339", "IPY_MODEL_53eb6abee62f49499cd0671ca53a5509", "IPY_MODEL_5f97dc511d5a49cca83c8810e272abc2", "IPY_MODEL_ffe7b64a9d714c04949a20d7cecd84c2", "IPY_MODEL_09bbd562f7134a57a6d56a36d41daa23", "IPY_MODEL_5b5d4db33b4d44088fff7f91127b23e2"], "layout": "IPY_MODEL_152c1e9ef01b4c078e2ac9a2a450b89f"}}, "cd1eeaec65554cb6b410a3403683103e": {"model_module": "@jupyter-widgets/controls", "model_name": "ButtonModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ButtonModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ButtonView", "button_style": "primary", "description": "Create Torrent", "disabled": false, "icon": "check", "layout": "IPY_MODEL_b3f4caabb5a74121b528af18f57c204b", "style": "IPY_MODEL_c959959fb86646e399ce8d2069851a9a", "tooltip": "Click to create torrent"}}, "01357233b8444261999638b2821a143b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_33e3feb4cb02417d9e6f40cc1a3881c1", "placeholder": "​", "style": "IPY_MODEL_b84518394a414c2787290e99fe12ca49", "value": "<div class=\"loader\"></div>"}}, "6d53d0d9969a4fc7b7711801fb71ba31": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "model_module_version": "1.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_187003d9379448109a7127ea88f52ec5", "msg_id": "", "outputs": []}}, "0c17e545386f44ef91972dab303008d8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_992eabf3533c471b94b3446850f50b11", "placeholder": "​", "style": "IPY_MODEL_f6297debc67c4b30b8ff914fb54a5fea", "value": ""}}, "e09a60b3518145f4aa8048dcfbb9d10c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": "1px solid #ddd", "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": "20px", "right": null, "top": null, "visibility": null, "width": "100%"}}, "4c1e8353eda0441b932db04f94badd3b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "00ac08cdb7b34dc3b28c815e30e8ec95": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "81bbc52a95084b018dfe70b008bbd7fd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "80%"}}, "663594b1d1ea43f38a4311fd3a0c258d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "4a6dfa1b025e461a97f9dc1b9bcde46d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3777b719f6a24a6389b65e4dea9300a7": {"model_module": "@jupyter-widgets/controls", "model_name": "ToggleButtonsStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ToggleButtonsStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_width": "", "description_width": "initial", "font_weight": ""}}, "2e481d00aa3f427c8556703bbd070a1f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "80%"}}, "1c260f8f16514b8da6f65b6285ef50e9": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "acb195c4fc23429cbc02f74ffa96f54d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "200px"}}, "d490a1616e1549ea9fb34f35b485e49b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "7ccdf13407dc4dabba45164021d0073c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b885310e230a404387f33da122b3bc36", "placeholder": "​", "style": "IPY_MODEL_09facb7c405349da9616901d286c600e", "value": "<h4>Media Information</h4>"}}, "4c8961f45fd14de492894bcd29e8c339": {"model_module": "@jupyter-widgets/controls", "model_name": "DropdownModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DropdownModel", "_options_labels": ["movie", "series"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "DropdownView", "description": "Type:", "description_tooltip": null, "disabled": false, "index": 0, "layout": "IPY_MODEL_288fd7c5bfa4406cbf3f5fa21e0ce489", "style": "IPY_MODEL_73bf0b41de5d454aafb7f4c62121ca8e"}}, "53eb6abee62f49499cd0671ca53a5509": {"model_module": "@jupyter-widgets/controls", "model_name": "TextModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "TextModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "TextView", "continuous_update": true, "description": "Title:", "description_tooltip": null, "disabled": false, "layout": "IPY_MODEL_dde9cf0d1b124d908445d82a5d9dd162", "placeholder": "Enter title", "style": "IPY_MODEL_884b732934394ce5829497feef42fed7", "value": ""}}, "5f97dc511d5a49cca83c8810e272abc2": {"model_module": "@jupyter-widgets/controls", "model_name": "TextModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "TextModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "TextView", "continuous_update": true, "description": "Year:", "description_tooltip": null, "disabled": false, "layout": "IPY_MODEL_f76ba60796054bffa5968f21ab23d278", "placeholder": "Optional: Year", "style": "IPY_MODEL_aae933c5c49c490fad99255389921ad8", "value": ""}}, "ffe7b64a9d714c04949a20d7cecd84c2": {"model_module": "@jupyter-widgets/controls", "model_name": "IntTextModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntTextModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "IntTextView", "continuous_update": false, "description": "Season:", "description_tooltip": null, "disabled": false, "layout": "IPY_MODEL_34b47e7eb5154ce58c39f4bcc9a57b84", "step": 1, "style": "IPY_MODEL_bdb82d4baf364284935f0e4029ce11e3", "value": 1}}, "09bbd562f7134a57a6d56a36d41daa23": {"model_module": "@jupyter-widgets/controls", "model_name": "IntTextModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "IntTextModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "IntTextView", "continuous_update": false, "description": "Episode:", "description_tooltip": null, "disabled": false, "layout": "IPY_MODEL_8baf8d27fb7d4c6894cdf7a6ed1b7a2b", "step": 1, "style": "IPY_MODEL_a3fd88efaac44ad484c2e1d692e21393", "value": 1}}, "5b5d4db33b4d44088fff7f91127b23e2": {"model_module": "@jupyter-widgets/controls", "model_name": "VBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "VBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "VBoxView", "box_style": "", "children": ["IPY_MODEL_3e4f975300d547bfb0d8bbe3bf704c5d", "IPY_MODEL_cf65722c7f3347d6aa12db29f491fc15", "IPY_MODEL_d3dfcdc9098a4213b26abdc6d5fcdb6b", "IPY_MODEL_c006b6128f92459d9915dca83284af95", "IPY_MODEL_3e651ee6da54499c96aa85995de2cc84", "IPY_MODEL_1735e1b5bcee4ecc983a75152db6edd0"], "layout": "IPY_MODEL_7a89e13edc9944ca98cd0f432a59c919"}}, "152c1e9ef01b4c078e2ac9a2a450b89f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "none", "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b3f4caabb5a74121b528af18f57c204b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "20px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "200px"}}, "c959959fb86646e399ce8d2069851a9a": {"model_module": "@jupyter-widgets/controls", "model_name": "ButtonStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ButtonStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "button_color": null, "font_weight": ""}}, "33e3feb4cb02417d9e6f40cc1a3881c1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "none", "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b84518394a414c2787290e99fe12ca49": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "992eabf3533c471b94b3446850f50b11": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "none", "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f6297debc67c4b30b8ff914fb54a5fea": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b885310e230a404387f33da122b3bc36": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "09facb7c405349da9616901d286c600e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "288fd7c5bfa4406cbf3f5fa21e0ce489": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "200px"}}, "73bf0b41de5d454aafb7f4c62121ca8e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "dde9cf0d1b124d908445d82a5d9dd162": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "80%"}}, "884b732934394ce5829497feef42fed7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "f76ba60796054bffa5968f21ab23d278": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "200px"}}, "aae933c5c49c490fad99255389921ad8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "34b47e7eb5154ce58c39f4bcc9a57b84": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "none", "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "150px"}}, "bdb82d4baf364284935f0e4029ce11e3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "8baf8d27fb7d4c6894cdf7a6ed1b7a2b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": "none", "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "150px"}}, "a3fd88efaac44ad484c2e1d692e21393": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "3e4f975300d547bfb0d8bbe3bf704c5d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bfd2b708706746c0931171a9afbb45e2", "placeholder": "​", "style": "IPY_MODEL_a8c3d93c1a7e4f09950fc85b916f1503", "value": "<h4>Quality Information</h4>"}}, "cf65722c7f3347d6aa12db29f491fc15": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ba7dda4bb6f5478d8d1bc8b4327edd1e", "IPY_MODEL_b9eb9edf5d614135aa46ca31fb52ac42"], "layout": "IPY_MODEL_929bc0d405a541d589ec20c4517f0972"}}, "d3dfcdc9098a4213b26abdc6d5fcdb6b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1bce3a707ab64cb492a7d529e66b804c", "IPY_MODEL_1337c90ee0f4498680abff41da6544c1", "IPY_MODEL_7806daf8d7dc4bbfa96a53d352612d09"], "layout": "IPY_MODEL_3f07d3b11eb44c20be3fa01c97affb4e"}}, "c006b6128f92459d9915dca83284af95": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_61642aa8974444e18ce25482ae9a0e5e", "placeholder": "​", "style": "IPY_MODEL_c6e32f9c079e4ff59af071d4de1d309c", "value": "<h4>Language Information</h4>"}}, "3e651ee6da54499c96aa85995de2cc84": {"model_module": "@jupyter-widgets/controls", "model_name": "SelectMultipleModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "SelectMultipleModel", "_options_labels": ["English", "Spanish", "French", "German", "Italian", "Russian", "Japanese", "Korean", "Chinese", "Hindi", "Tamil", "Telugu", "Malayalam", "Kannada", "Portuguese", "Turkish", "Arabic", "Thai", "Vietnamese", "Indonesian"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "SelectMultipleView", "description": "Languages:", "description_tooltip": null, "disabled": false, "index": [], "layout": "IPY_MODEL_76a6655a459f480a86abda8299000fb2", "rows": 5, "style": "IPY_MODEL_c30d9fdff73745ab9d453ea2ccf5c857"}}, "1735e1b5bcee4ecc983a75152db6edd0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c638b489b6ee4cbcbd833d46f8af8401", "placeholder": "​", "style": "IPY_MODEL_b1e663dd4a1a4668bcc1f083fecb6a40", "value": "<small>Hold Ctrl/Cmd to select multiple languages</small>"}}, "7a89e13edc9944ca98cd0f432a59c919": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bfd2b708706746c0931171a9afbb45e2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a8c3d93c1a7e4f09950fc85b916f1503": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ba7dda4bb6f5478d8d1bc8b4327edd1e": {"model_module": "@jupyter-widgets/controls", "model_name": "DropdownModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DropdownModel", "_options_labels": ["", "480p", "576p", "720p", "1080p", "1440p", "2160p", "4K"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "DropdownView", "description": "Resolution:", "description_tooltip": null, "disabled": false, "index": 0, "layout": "IPY_MODEL_d2ed8e7b0c864caa84c87dd34783cfcd", "style": "IPY_MODEL_dc7355250fb3489197d9f210023061be"}}, "b9eb9edf5d614135aa46ca31fb52ac42": {"model_module": "@jupyter-widgets/controls", "model_name": "DropdownModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DropdownModel", "_options_labels": ["", "BluRay", "BluRay REMUX", "BRRip", "BDRip", "WEB-DL", "HDRip", "DVDRip", "HDTV", "CAM", "TeleSync", "SCR"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "DropdownView", "description": "Source:", "description_tooltip": null, "disabled": false, "index": 0, "layout": "IPY_MODEL_a5459264ce2444758b770c5d2323250a", "style": "IPY_MODEL_cef3d4af029144ba94e009af4a675550"}}, "929bc0d405a541d589ec20c4517f0972": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1bce3a707ab64cb492a7d529e66b804c": {"model_module": "@jupyter-widgets/controls", "model_name": "DropdownModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DropdownModel", "_options_labels": ["", "x264", "x265", "H.264", "H.265", "HEVC", "AVC", "MPEG-2", "MPEG-4", "VP9"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "DropdownView", "description": "Video Codec:", "description_tooltip": null, "disabled": false, "index": 0, "layout": "IPY_MODEL_8d87b2887db34c5cbe0122a1fcaf085f", "style": "IPY_MODEL_547474f223c94b888e8e30c666f6371c"}}, "1337c90ee0f4498680abff41da6544c1": {"model_module": "@jupyter-widgets/controls", "model_name": "DropdownModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DropdownModel", "_options_labels": ["", "AAC", "AC3", "DTS", "DTS-HD MA", "TrueHD", "Atmos", "DD+"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "DropdownView", "description": "Audio Codec:", "description_tooltip": null, "disabled": false, "index": 0, "layout": "IPY_MODEL_ed2a9f01369a4f55be2208e2d2a0f1a8", "style": "IPY_MODEL_7a6159e116fa43ebbe3c4cb1563ef875"}}, "7806daf8d7dc4bbfa96a53d352612d09": {"model_module": "@jupyter-widgets/controls", "model_name": "DropdownModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DropdownModel", "_options_labels": ["", "2.0", "5.1", "7.1"], "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "DropdownView", "description": "Audio Channels:", "description_tooltip": null, "disabled": false, "index": 0, "layout": "IPY_MODEL_177c0115fffb4192bc547c595823db1a", "style": "IPY_MODEL_65092a26f43645279b4fd8fb6664e47c"}}, "3f07d3b11eb44c20be3fa01c97affb4e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "61642aa8974444e18ce25482ae9a0e5e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6e32f9c079e4ff59af071d4de1d309c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "76a6655a459f480a86abda8299000fb2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": "150px", "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "300px"}}, "c30d9fdff73745ab9d453ea2ccf5c857": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "c638b489b6ee4cbcbd833d46f8af8401": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "0px 0px 10px 10px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1e663dd4a1a4668bcc1f083fecb6a40": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d2ed8e7b0c864caa84c87dd34783cfcd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "200px"}}, "dc7355250fb3489197d9f210023061be": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "a5459264ce2444758b770c5d2323250a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "200px"}}, "cef3d4af029144ba94e009af4a675550": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "8d87b2887db34c5cbe0122a1fcaf085f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "200px"}}, "547474f223c94b888e8e30c666f6371c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "ed2a9f01369a4f55be2208e2d2a0f1a8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "200px"}}, "7a6159e116fa43ebbe3c4cb1563ef875": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "177c0115fffb4192bc547c595823db1a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "200px"}}, "65092a26f43645279b4fd8fb6664e47c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": "initial"}}, "187003d9379448109a7127ea88f52ec5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": "1px solid black", "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": "10px 0px", "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": "10px", "right": null, "top": null, "visibility": null, "width": null}}}}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/mhdzumair/MediaFusion/blob/main/docs/TorrentWebCreator.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# WebSeed Torrent Creator\n", "**DISCLAIMER**: This notebook is provided for educational purposes only. It demonstrates the technical concepts of creating torrents with web seeds, proper media naming conventions, and metadata organization. Users are responsible for ensuring they comply with all applicable laws and have the necessary rights for any content they process.\n", "\n", "\n", "## Using the Creator\n", "1. Click on **Runtime** -> **Run all**\n", "2. **Scroll to the bottom** of the notebook to find the input interface\n", "\n", "3. Choose your preferred naming method:\n", "   - **Direct Filename**: Enter complete name manually\n", "   - **Use Metadata**: Build name using structured fields\n", "\n", "4. Required <PERSON>:\n", "   - URL: Source URL for the content\n", "   - Filename or Title (depending on mode)\n", "   - File Type (e.g., mkv, mp4)\n", "\n", "5. Optional Metadata Fields (if using metadata mode):\n", "   - Media Type (movie/series)\n", "   - Year\n", "   - Season/Episode (for series)\n", "   - Resolution (480p to 4K)\n", "   - Source (BluRay, WEB-DL, etc.)\n", "   - Video/Audio codecs\n", "   - Languages (multiple selectable)\n", "\n", "6. <PERSON>lick \"Create Torrent\" and wait for processing\n", "7. Use the download button to get your torrent file\n", "\n"], "metadata": {"id": "ZcHohv30cKGC"}}, {"cell_type": "code", "metadata": {"id": "2kvN6iGA7pG7", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "4e52df64-d877-43a2-e5ec-b917f7e02890"}, "source": ["!pip install ipywidgets requests\n", "!apt-get -qq install aria2\n", "!wget -qO torrenttools.AppImage https://github.com/fbdtemme/torrenttools/releases/download/v0.6.2/torrenttools-0.6.2-linux-x86_64.AppImage\n", "!chmod +x torrenttools.AppImage\n", "!./torrenttools.AppImage --appimage-extract > /dev/null"], "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["import base64\n", "import os\n", "import subprocess\n", "import threading\n", "from typing import Dict, Optional, List\n", "from urllib.parse import urlparse, unquote\n", "\n", "import ipywidgets as widgets\n", "import requests\n", "from IPython.display import display, HTML\n", "\n", "\n", "class MediaTorrentCreator:\n", "    # Constants for media information\n", "    RESOLUTIONS = ['480p', '576p', '720p', '1080p', '1440p', '2160p', '4K']\n", "    SOURCES = ['BluRay', 'BluRay REMUX', 'BRRip', 'BDRip', 'WEB-DL', 'HDRip', 'DVDRip', 'HDTV', 'CAM', 'TeleSync', 'SCR']\n", "    AUDIO_CODECS = ['AAC', 'AC3', 'DTS', 'DTS-HD MA', 'TrueHD', 'Atmos', 'DD+']\n", "    VIDEO_CODECS = ['x264', 'x265', 'H.264', 'H.265', 'HEVC', 'AVC', 'MPEG-2', 'MPEG-4', 'VP9']\n", "    AUDIO_CHANNELS = ['2.0', '5.1', '7.1']\n", "    FILE_TYPES = ['mp4', 'mkv', 'avi', 'webm']\n", "    LANGUAGES = [\n", "        'English', 'Spanish', 'French', 'German', 'Italian', 'Russian', 'Japanese',\n", "        'Korean', 'Chinese', 'Hindi', 'Tamil', 'Telugu', 'Malayalam', 'Kannada',\n", "        'Portuguese', 'Turkish', 'Arabic', 'Thai', 'Vietnamese', 'Indonesian'\n", "    ]\n", "    LANGUAGE_CODES = {\n", "        'English': 'ENG', 'Spanish': 'SPA', 'French': 'FRE', 'German': 'GER',\n", "        'Italian': 'ITA', 'Russian': 'RUS', 'Japanese': 'JPN', 'Korean': 'KOR',\n", "        'Chinese': 'CHI', 'Hindi': 'HIN', 'Tamil': 'TAM', 'Telugu': 'TEL',\n", "        'Malayalam': 'MAL', 'Kannada': 'KAN', 'Portuguese': 'POR', 'Turkish': 'TUR',\n", "        'Arabic': 'ARA', 'Thai': 'THA', 'Vietnamese': 'VIE', 'Indonesian': 'IND'\n", "    }\n", "\n", "    TRACKERS = {\n", "        \"udp://tracker.renfei.net:8080/announce\",\n", "        \"udp://fe.dealclub.de:6969/announce\",\n", "        \"udp://boysbitte.be:6969/announce\",\n", "        \"udp://thouvenin.cloud:6969/announce\",\n", "        \"udp://tracker.theoks.net:6969/announce\",\n", "        \"udp://wepzone.net:6969/announce\",\n", "        \"https://tracker.foreverpirates.co:443/announce\",\n", "        \"udp://ipv4.tracker.harry.lu:80/announce\",\n", "        \"udp://tracker.dler.org:6969/announce\",\n", "        \"udp://camera.lei001.com:6969/announce\",\n", "        \"http://tracker.mywaifu.best:6969/announce\",\n", "        \"udp://tracker.ddunlimited.net:6969/announce\",\n", "        \"http://bt.endpot.com:80/announce\",\n", "        \"http://tracker.ipv6tracker.ru:80/announce\",\n", "        \"udp://static.*************.clients.your-server.de:6969/announce\",\n", "        \"http://bt.okmp3.ru:2710/announce\",\n", "        \"udp://tracker.bittor.pw:1337/announce\",\n", "        \"udp://tracker.jonaslsa.com:6969/announce\",\n", "        \"udp://tracker.ccp.ovh:6969/announce\",\n", "        \"http://vps-dd0a0715.vps.ovh.net:6969/announce\",\n", "        \"http://wepzone.net:6969/announce\",\n", "        \"udp://psyco.fr:6969/announce\",\n", "        \"udp://9.rarbg.com:2810/announce\",\n", "        \"https://opentracker.i2p.rocks:443/announce\",\n", "        \"http://1337.abcvg.info:80/announce\",\n", "        \"udp://open.tracker.cl:1337/announce\",\n", "        \"udp://opentracker.io:6969/announce\",\n", "        \"udp://u4.trakx.crim.ist:1337/announce\",\n", "        \"http://tracker.opentrackr.org:1337/announce\",\n", "        \"udp://mail.artixlinux.org:6969/announce\",\n", "        \"udp://tracker.publictracker.xyz:6969/announce\",\n", "        \"udp://moonburrow.club:6969/announce\",\n", "        \"udp://tracker.moeking.me:6969/announce\",\n", "        \"udp://tr.bangumi.moe:6969/announce\",\n", "        \"https://tracker.gbitt.info:443/announce\",\n", "        \"http://v6-tracker.0g.cx:6969/announce\",\n", "        \"udp://public.publictracker.xyz:6969/announce\",\n", "        \"udp://www.torrent.eu.org:451/announce\",\n", "        \"https://tracker.kuroy.me:443/announce\",\n", "        \"https://tr.ready4.icu:443/announce\",\n", "        \"https://tracker.lelux.fi:443/announce\",\n", "        \"https://torrent-tracker.hama3.net:443/announce\",\n", "        \"udp://black-bird.ynh.fr:6969/announce\",\n", "        \"udp://tracker.artixlinux.org:6969/announce\",\n", "        \"udp://v2.iperson.xyz:6969/announce\",\n", "        \"https://tracker.mlsub.net:443/announce\",\n", "        \"http://tracker.dler.org:6969/announce\",\n", "        \"http://tracker.qu.ax:6969/announce\",\n", "        \"udp://tamas3.ynh.fr:6969/announce\",\n", "        \"udp://tracker.yangxiaoguozi.cn:6969/announce\",\n", "        \"udp://v1046920.hosted-by-vdsina.ru:6969/announce\",\n", "        \"udp://tracker.dler.com:6969/announce\",\n", "        \"udp://admin.52ywp.com:6969/announce\",\n", "        \"http://bt1.letpo.com:80/announce\",\n", "        \"http://tracker.lelux.fi:80/announce\",\n", "        \"https://1337.abcvg.info:443/announce\",\n", "        \"udp://thagoat.rocks:6969/announce\",\n", "        \"https://tracker1.520.jp:443/announce\",\n", "        \"udp://chouchou.top:8080/announce\",\n", "        \"udp://fh2.cmp-gaming.com:6969/announce\",\n", "        \"http://parag.rs:6969/announce\",\n", "        \"udp://tracker.swateam.org.uk:2710/announce\",\n", "        \"udp://trackerb.jonaslsa.com:6969/announce\",\n", "        \"udp://open.publictracker.xyz:6969/announce\",\n", "        \"udp://open.demonii.com:1337/announce\",\n", "        \"http://tracker1.bt.moack.co.kr:80/announce\",\n", "        \"udp://epider.me:6969/announce\",\n", "        \"udp://tracker.cyberia.is:6969/announce\",\n", "        \"udp://run.publictracker.xyz:6969/announce\",\n", "        \"udp://run-2.publictracker.xyz:6969/announce\",\n", "        \"udp://6ahddutb1ucc3cp.ru:6969/announce\",\n", "        \"http://tracker.bt4g.com:2095/announce\"\n", "    }\n", "\n", "    def __init__(self, base_path: str = \"./media_storage\"):\n", "        \"\"\"Initialize the creator with local storage paths\"\"\"\n", "        self.base_path = base_path\n", "        self.create_storage_directories()\n", "\n", "    def create_storage_directories(self):\n", "        \"\"\"Create necessary directories for local storage\"\"\"\n", "        directories = ['downloads', 'torrents']\n", "        for dir_name in directories:\n", "            path = os.path.join(self.base_path, dir_name)\n", "            os.makedirs(path, exist_ok=True)\n", "            print(f\"Created directory: {path}\")\n", "\n", "    def _fetch_best_trackers(self) -> List[str]:\n", "        \"\"\"Fetch best trackers from <PERSON><PERSON><PERSON>'s trackerslist\"\"\"\n", "        try:\n", "            response = requests.get(\n", "                \"https://raw.githubusercontent.com/ngosang/trackerslist/master/trackers_best.txt\",\n", "                timeout=30\n", "            )\n", "            if response.status_code == 200:\n", "                best_trackers = {tracker.strip() for tracker in response.text.split(\"\\n\") if tracker.strip()}\n", "                return list(best_trackers.union(self.TRACKERS))\n", "        except Exception as e:\n", "            print(f\"Warning: Failed to fetch trackers: {str(e)}\")\n", "        return list(self.TRACKERS)\n", "\n", "    def _get_filename_from_url(self, url: str, custom_filename: Optional[str] = None) -> str:\n", "        \"\"\"Get filename from URL or custom input\"\"\"\n", "        if custom_filename:\n", "            return custom_filename\n", "\n", "        try:\n", "            # Try to get filename from Content-Disposition header\n", "            response = requests.head(url, allow_redirects=True, timeout=10)\n", "            if 'Content-Disposition' in response.headers:\n", "                content_disp = response.headers['Content-Disposition']\n", "                if 'filename=' in content_disp:\n", "                    return content_disp.split('filename=')[-1].strip('\"\\'')\n", "\n", "            # Try to get filename from URL path\n", "            parsed_url = urlparse(url)\n", "            url_path = unquote(parsed_url.path)\n", "            filename = os.path.basename(url_path)\n", "\n", "            if filename and '.' in filename:\n", "                return filename\n", "\n", "            # Use Content-Type for extension if available\n", "            if 'Content-Type' in response.headers:\n", "                content_type = response.headers['Content-Type'].split(';')[0]\n", "                ext_map = {\n", "                    'video/mp4': '.mp4',\n", "                    'video/x-matroska': '.mkv',\n", "                    'video/webm': '.webm',\n", "                    'application/x-bittorrent': '.torrent'\n", "                }\n", "                ext = ext_map.get(content_type, '')\n", "                if ext:\n", "                    return f'download{ext}'\n", "\n", "        except Exception as e:\n", "            print(f\"Warning: Error in filename detection: {str(e)}\")\n", "\n", "        return 'download'\n", "\n", "    def _generate_torrent_name(self, media_info: Dict) -> str:\n", "        \"\"\"Generate structured torrent name from media information\"\"\"\n", "        parts = []\n", "\n", "        # Add title and year\n", "        title = media_info.get('title', '').replace(' ', '.')\n", "        year = media_info.get('year', '')\n", "        parts.append(f\"{title}{f'.{year}' if year else ''}\")\n", "\n", "        # Add season and episode for series\n", "        if media_info.get('media_type') == 'series':\n", "            season = media_info.get('season')\n", "            episode = media_info.get('episode')\n", "            if season and episode:\n", "                parts.append(f\"S{int(season):02d}E{int(episode):02d}\")\n", "\n", "        # Add quality info\n", "        resolution = media_info.get('resolution', '')\n", "        source = media_info.get('source', '')\n", "        if resolution and source:\n", "            parts.append(f\"{resolution}.{source}\")\n", "        elif resolution:\n", "            parts.append(resolution)\n", "        elif source:\n", "            parts.append(source)\n", "\n", "        # Add language info\n", "        languages = media_info.get('languages', [])\n", "        if languages:\n", "            # Convert full language names to codes and join them\n", "            language_codes = [self.LANGUAGE_CODES.get(lang, lang) for lang in languages]\n", "            parts.append('-'.join(language_codes))\n", "\n", "        # Add codec info\n", "        video_codec = media_info.get('video_codec', '')\n", "        if video_codec:\n", "            parts.append(video_codec)\n", "\n", "        # Add audio info\n", "        audio_codec = media_info.get('audio_codec', '')\n", "        audio_channels = media_info.get('audio_channels', '')\n", "        if audio_codec and audio_channels:\n", "            parts.append(f\"{audio_codec}.{audio_channels}\")\n", "        elif audio_codec:\n", "            parts.append(audio_codec)\n", "\n", "        return '.'.join(parts)\n", "\n", "    def _monitor_download_progress(self, process, progress_widget, status_label):\n", "        \"\"\"Monitor aria2c download progress and update the progress widget\"\"\"\n", "        try:\n", "            total_size = None\n", "            max_total_seen = 0  # Keep track of the largest total size we've seen\n", "            last_progress = 0  # Keep track of the last progress value\n", "\n", "            for line in iter(process.stdout.readline, ''):\n", "                try:\n", "                    if '[#' in line:  # This is a progress line\n", "                        # Example line: [#7a9390 16MiB/34MiB(47%) CN:1 DL:6.2MiB ETA:2s]\n", "                        parts = line.strip().split()\n", "\n", "                        # Find the part containing the size information (e.g., \"16MiB/34MiB\")\n", "                        size_part = next((part for part in parts if '/' in part and 'iB' in part), None)\n", "                        if size_part:\n", "                            current, total = size_part.split('/')\n", "                            current_mb = self._parse_size(current)\n", "                            total_mb = self._parse_size(total)\n", "\n", "                            # Update max total size if this is the largest we've seen\n", "                            if total_mb > max_total_seen:\n", "                                max_total_seen = total_mb\n", "                                total_size = total_mb  # Update our fixed total size\n", "\n", "                            # Calculate progress based on the fixed total size\n", "                            if total_size:\n", "                                progress = min((current_mb / total_size) * 100, 100)\n", "\n", "                                # Only update if progress has increased\n", "                                if progress > last_progress:\n", "                                    last_progress = progress\n", "                                    progress_widget.value = progress\n", "\n", "                            # Find download speed\n", "                            speed_part = next((part for part in parts if 'DL:' in part), None)\n", "                            if speed_part:\n", "                                speed = self._parse_size(speed_part.replace('DL:', ''))\n", "                                speed_text = f\"{speed:.2f}MB/s\"\n", "\n", "                                # Update status text with fixed total size\n", "                                status_text = (\n", "                                    f\"Downloaded: {current_mb:.1f}MB / {total_size:.1f}MB \"\n", "                                    f\"({progress:.1f}%) • Speed: {speed_text}\"\n", "                                )\n", "                                status_label.value = status_text\n", "\n", "                except Exception as e:\n", "                    continue  # Skip any lines we can't parse\n", "\n", "            # Ensure we show 100% at completion\n", "            progress_widget.value = 100\n", "            status_label.value = \"Download complete!\"\n", "\n", "        except Exception as e:\n", "            print(f\"Error monitoring progress: {str(e)}\")\n", "            status_label.value = \"Error monitoring progress\"\n", "\n", "    def _parse_size(self, size_str):\n", "        \"\"\"Convert aria2c size strings to MB\"\"\"\n", "        try:\n", "            # Extract numeric part and unit\n", "            size_str = size_str.strip()\n", "\n", "            # Handle percentage or other non-size strings\n", "            if not any(unit in size_str for unit in ['KiB', 'MiB', 'GiB', 'TiB']):\n", "                return 0\n", "\n", "            number = float(''.join(filter(lambda x: x.isdigit() or x == '.', size_str)))\n", "            unit = ''.join(filter(str.isalpha, size_str))\n", "\n", "            # Convert to MB based on unit\n", "            multipliers = {\n", "                'KiB': 1 / 1024,\n", "                'MiB': 1,\n", "                'GiB': 1024,\n", "                'TiB': 1024 * 1024\n", "            }\n", "\n", "            return number * multipliers.get(unit, 1)\n", "        except Exception:\n", "            return 0\n", "\n", "    def create_torrent(\n", "        self,\n", "        source_url: str,\n", "        media_info: Dict,\n", "        custom_filename: Optional[str] = None,\n", "        cleanup: bool = False,\n", "        progress_widget=None,\n", "        status_label=None\n", "    ) -> Dict:\n", "        \"\"\"Create a torrent with web seeding for content\"\"\"\n", "        try:\n", "            download_dir = os.path.join(self.base_path, \"downloads\")\n", "            trackers = self._fetch_best_trackers()\n", "\n", "            # Get appropriate filename for download\n", "            download_filename = self._get_filename_from_url(source_url, custom_filename)\n", "            safe_download_filename = \"\".join(c for c in download_filename if c.isalnum() or c in \"._- \").strip()\n", "            local_file_path = os.path.join(download_dir, safe_download_filename)\n", "\n", "            # Generate torrent name\n", "            torrent_name = self._generate_torrent_name(media_info)\n", "\n", "            # Download with aria2c\n", "            print(f\"Downloading using aria2c to: {safe_download_filename}\")\n", "            aria2c_command = [\n", "                \"aria2c\",\n", "                \"-x\", \"16\",  # Maximum connection per server\n", "                \"-s\", \"16\",  # Split file into 16 parts\n", "                \"-k\", \"1M\",  # Minimum split size\n", "                \"-o\", safe_download_filename,\n", "                \"-d\", download_dir,\n", "                \"--summary-interval=1\",  # Update interval in seconds\n", "                \"--show-console-readout=true\",\n", "                \"--auto-file-renaming=false\",\n", "                source_url\n", "            ]\n", "\n", "            process = subprocess.Popen(\n", "                aria2c_command,\n", "                stdout=subprocess.PIPE,\n", "                stderr=subprocess.PIPE,\n", "                text=True,\n", "                bufsize=1,\n", "                universal_newlines=True\n", "            )\n", "\n", "            if progress_widget and status_label:\n", "                # Start progress monitoring in a separate thread\n", "                monitor_thread = threading.Thread(\n", "                    target=self._monitor_download_progress,\n", "                    args=(process, progress_widget, status_label),\n", "                    daemon=True\n", "                )\n", "                monitor_thread.start()\n", "\n", "            # Wait for download to complete\n", "            stdout, stderr = process.communicate()\n", "\n", "            if process.returncode != 0:\n", "                raise Exception(f\"Failed to download file: {stderr}\")\n", "\n", "            if not os.path.exists(local_file_path):\n", "                raise Exception(f\"Downloaded file not found: {local_file_path}\")\n", "\n", "            file_size = os.path.getsize(local_file_path)  # Size in bytes\n", "            if file_size < 10 * 1024 * 1024:  # 10 MB in bytes\n", "                raise Exception(f\"Downloaded file size is less than 10MB (size: {file_size / (1024 * 1024):.2f} MB)\")\n", "\n", "            # Create torrent file\n", "            output_path = os.path.join(self.base_path, \"torrents\", f\"{torrent_name}.torrent\")\n", "\n", "            create_command = [\n", "                \"./squashfs-root/AppRun\",\n", "                \"create\",\n", "                local_file_path,\n", "                \"-v\", \"1\",\n", "                \"-n\", torrent_name,\n", "                \"-o\", output_path,\n", "                \"-w\", source_url,\n", "                \"--announce\", *trackers,\n", "                \"-l\", \"auto\",\n", "                \"-s\", \"WebSeedTorrentCreator\",\n", "                \"--created-by\", \"WebSeedTorrentCreator\"\n", "            ]\n", "\n", "            process = subprocess.run(\n", "                create_command,\n", "                stdout=subprocess.PIPE,\n", "                stderr=subprocess.PIPE,\n", "                text=True\n", "            )\n", "\n", "            if process.returncode != 0:\n", "                raise Exception(f\"Failed to create torrent: {process.stderr}\")\n", "\n", "            if cleanup:\n", "                try:\n", "                    os.remove(local_file_path)\n", "                    print(f\"Cleaned up downloaded file: {local_file_path}\")\n", "                except Exception as e:\n", "                    print(f\"Warning: Failed to clean up file: {str(e)}\")\n", "\n", "            return {\n", "                \"torrent_path\": output_path,\n", "                \"web_seed_url\": source_url,\n", "                \"local_file\": local_file_path,\n", "                \"filename\": safe_download_filename,\n", "                \"torrent_name\": torrent_name,\n", "                \"trackers\": trackers,\n", "                \"command_output\": stdout\n", "            }\n", "\n", "        except Exception as e:\n", "            raise Exception(f\"Error in create_torrent: {str(e)}\")\n", "\n", "    def create_torrent_gui(self):\n", "        \"\"\"Create GUI with structured naming options\"\"\"\n", "        # Create all input widgets\n", "        url_input = widgets.Text(\n", "            value='',\n", "            placeholder='Enter URL',\n", "            description='URL:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='80%', margin='10px 0px')\n", "        )\n", "\n", "        naming_mode = widgets.ToggleButtons(\n", "            options=['Direct Filename', 'Use Metadata'],\n", "            description='Naming Mode:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(margin='10px 0px')\n", "        )\n", "\n", "        direct_filename = widgets.Text(\n", "            value='',\n", "            placeholder='Enter complete filename (e.g., Movie.Name.2024.1080p.BluRay.x264)',\n", "            description='Filename:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='80%', margin='10px 0px')\n", "        )\n", "\n", "        file_type = widgets.Dropdown(\n", "            options=self.FILE_TYPES,\n", "            value='mkv',\n", "            description='File Type:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='200px', margin='10px 0px')\n", "        )\n", "\n", "        media_type = widgets.Dropdown(\n", "            options=['movie', 'series'],\n", "            value='movie',\n", "            description='Type:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='200px', margin='10px 0px')\n", "        )\n", "\n", "        title_input = widgets.Text(\n", "            value='',\n", "            placeholder='Enter title',\n", "            description='Title:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='80%', margin='10px 0px')\n", "        )\n", "\n", "        year_input = widgets.Text(\n", "            value='',\n", "            placeholder='Optional: Year',\n", "            description='Year:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='200px', margin='10px 0px')\n", "        )\n", "\n", "        season_input = widgets.IntText(\n", "            value=1,\n", "            description='Season:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='150px', margin='10px 0px', display='none')\n", "        )\n", "\n", "        episode_input = widgets.IntText(\n", "            value=1,\n", "            description='Episode:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='150px', margin='10px 0px', display='none')\n", "        )\n", "\n", "        resolution_input = widgets.Dropdown(\n", "            options=[''] + self.RESOLUTIONS,\n", "            value='',\n", "            description='Resolution:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='200px', margin='10px 0px')\n", "        )\n", "\n", "        source_input = widgets.Dropdown(\n", "            options=[''] + self.SOURCES,\n", "            value='',\n", "            description='Source:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='200px', margin='10px 0px')\n", "        )\n", "\n", "        video_codec_input = widgets.Dropdown(\n", "            options=[''] + self.VIDEO_CODECS,\n", "            value='',\n", "            description='Video Codec:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='200px', margin='10px 0px')\n", "        )\n", "\n", "        audio_codec_input = widgets.Dropdown(\n", "            options=[''] + self.AUDIO_CODECS,\n", "            value='',\n", "            description='Audio Codec:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='200px', margin='10px 0px')\n", "        )\n", "\n", "        audio_channels_input = widgets.Dropdown(\n", "            options=[''] + self.AUDIO_CHANNELS,\n", "            value='',\n", "            description='Audio Channels:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='200px', margin='10px 0px')\n", "        )\n", "\n", "        languages_select = widgets.SelectMultiple(\n", "            options=self.LANGUAGES,\n", "            value=[],\n", "            description='Languages:',\n", "            style={'description_width': 'initial'},\n", "            layout=widgets.Layout(width='300px', height='150px', margin='10px 0px')\n", "        )\n", "\n", "        language_description = widgets.HTML(\n", "            value='<small>Hold Ctrl/Cmd to select multiple languages</small>',\n", "            layout=widgets.Layout(margin='0px 0px 10px 10px')\n", "        )\n", "\n", "        create_button = widgets.Button(\n", "            description='Create Torrent',\n", "            button_style='primary',\n", "            tooltip='Click to create torrent',\n", "            icon='check',\n", "            layout=widgets.Layout(width='200px', margin='20px 0px')\n", "        )\n", "\n", "        output_area = widgets.Output(\n", "            layout={'border': '1px solid black', 'margin': '10px 0px', 'padding': '10px'}\n", "        )\n", "\n", "        download_area = widgets.HTML(\n", "            value='',\n", "            layout=widgets.Layout(display='none', margin='10px 0px')\n", "        )\n", "\n", "        loading_indicator = widgets.HTML(\n", "            value='<div class=\"loader\"></div>',\n", "            layout=widgets.Layout(display='none')\n", "        )\n", "\n", "        progress_bar = widgets.FloatProgress(\n", "            value=0,\n", "            min=0,\n", "            max=100,\n", "            description='Progress:',\n", "            bar_style='info',\n", "            style={'bar_color': '#2196F3'},\n", "            orientation='horizontal',\n", "            layout=widgets.Layout(width='80%', margin='10px 0px')\n", "        )\n", "\n", "        status_label = widgets.HTML(\n", "            value='Ready to download...',\n", "            layout=widgets.Layout(margin='5px 0px')\n", "        )\n", "\n", "        # Add loading spinner CSS\n", "        display(HTML(\"\"\"\n", "        <style>\n", "        .loader {\n", "            border: 4px solid #f3f3f3;\n", "            border-top: 4px solid #3498db;\n", "            border-radius: 50%;\n", "            width: 30px;\n", "            height: 30px;\n", "            animation: spin 1s linear infinite;\n", "            margin: 10px;\n", "        }\n", "        @keyframes spin {\n", "            0% { transform: rotate(0deg); }\n", "            100% { transform: rotate(360deg); }\n", "        }\n", "        </style>\n", "        \"\"\"))\n", "\n", "        def on_naming_mode_change(change):\n", "            if change['new'] == 'Direct Filename':\n", "                metadata_section.layout.display = 'none'\n", "                direct_filename.layout.display = 'block'\n", "            else:\n", "                metadata_section.layout.display = 'block'\n", "                direct_filename.layout.display = 'none'\n", "\n", "        def on_media_type_change(change):\n", "            if change['new'] == 'series':\n", "                season_input.layout.display = 'block'\n", "                episode_input.layout.display = 'block'\n", "            else:\n", "                season_input.layout.display = 'none'\n", "                episode_input.layout.display = 'none'\n", "\n", "        def create_download_button(torrent_path: str, filename: str):\n", "            \"\"\"Create a download button for the torrent file\"\"\"\n", "            try:\n", "                with open(torrent_path, 'rb') as f:\n", "                    torrent_data = f.read()\n", "                    b64_data = base64.b64encode(torrent_data).decode('utf-8')\n", "\n", "                download_html = f\"\"\"\n", "                <a href=\"data:application/x-bittorrent;base64,{b64_data}\"\n", "                   download=\"{filename}\"\n", "                   class=\"jupyter-button jupyter-widget btn btn-primary\"\n", "                   style=\"text-decoration: none; padding: 8px 16px; margin: 10px 0;\">\n", "                   📥 Download Torrent File\n", "                </a>\n", "                \"\"\"\n", "                download_area.value = download_html\n", "                download_area.layout.display = 'block'\n", "            except Exception as e:\n", "                print(f\"Error creating download button: {str(e)}\")\n", "\n", "        def on_create_button_click(b):\n", "            # Disable button and show loading\n", "            create_button.disabled = True\n", "            loading_indicator.layout.display = 'block'\n", "\n", "            # Reset progress\n", "            progress_bar.value = 0\n", "            status_label.value = 'Starting download...'\n", "            with output_area:\n", "                output_area.clear_output()\n", "                print(\"Starting torrent creation process...\")\n", "                try:\n", "                    # Determine torrent name based on mode\n", "                    if naming_mode.value == 'Direct Filename':\n", "                        media_info = {\n", "                            'title': direct_filename.value\n", "                        }\n", "                        torrent_name = f\"{direct_filename.value}.{file_type.value}\"\n", "                    else:\n", "                        media_info = {\n", "                            'media_type': media_type.value,\n", "                            'title': title_input.value,\n", "                            'year': year_input.value,\n", "                            'resolution': resolution_input.value,\n", "                            'source': source_input.value,\n", "                            'video_codec': video_codec_input.value,\n", "                            'audio_codec': audio_codec_input.value,\n", "                            'audio_channels': audio_channels_input.value,\n", "                            'languages': list(languages_select.value)\n", "                        }\n", "\n", "                        if media_type.value == 'series':\n", "                            media_info.update({\n", "                                'season': season_input.value,\n", "                                'episode': episode_input.value\n", "                            })\n", "\n", "                        torrent_name = self._generate_torrent_name(media_info)\n", "                        torrent_name = f\"{torrent_name}.{file_type.value}\"\n", "\n", "                    result = self.create_torrent(\n", "                        source_url=url_input.value,\n", "                        media_info={'title': torrent_name},\n", "                        custom_filename=f\"{torrent_name}\",\n", "                        progress_widget=progress_bar,\n", "                        status_label=status_label\n", "                    )\n", "\n", "                    print(\"\\n✅ Torrent created successfully!\")\n", "                    print(f\"📁 Torrent path: {result['torrent_path']}\")\n", "                    print(f\"🏷️ Torrent name: {result['torrent_name']}\")\n", "                    print(f\"📝 Download filename: {result['filename']}\")\n", "                    print(f\"🔗 Web seed URL: {result['web_seed_url']}\")\n", "\n", "                    # Create download button\n", "                    create_download_button(result['torrent_path'], os.path.basename(result['torrent_path']))\n", "\n", "                except Exception as e:\n", "                    print(f\"❌ Error creating torrent: {str(e)}\")\n", "                    print(\"\\nPlease check:\")\n", "                    print(\"1. Is the URL valid and accessible?\")\n", "                    print(\"2. Have you filled in all required fields?\")\n", "                    print(\"3. Do you have write permissions in the output directory?\")\n", "                    # Reset progress on error\n", "                    progress_bar.value = 0\n", "                    status_label.value = 'Error occurred during download'\n", "\n", "                finally:\n", "                    # Re-enable button and hide loading\n", "                    create_button.disabled = False\n", "                    loading_indicator.layout.display = 'none'\n", "\n", "        # Connect event handlers\n", "        naming_mode.observe(on_naming_mode_change, names='value')\n", "        media_type.observe(on_media_type_change, names='value')\n", "        create_button.on_click(on_create_button_click)\n", "\n", "        # Create layout sections\n", "        quality_info = widgets.VBox([\n", "            widgets.HTML(\"<h4>Quality Information</h4>\"),\n", "            widgets.HBox([resolution_input, source_input]),\n", "            widgets.HBox([video_codec_input, audio_codec_input, audio_channels_input]),\n", "            widgets.HTML(\"<h4>Language Information</h4>\"),\n", "            languages_select,\n", "            language_description\n", "        ])\n", "\n", "        # Metadata section contains all the structured input fields\n", "        metadata_section = widgets.VBox([\n", "            widgets.HTML(\"<h4>Media Information</h4>\"),\n", "            media_type,\n", "            title_input,\n", "            year_input,\n", "            season_input,\n", "            episode_input,\n", "            quality_info\n", "        ])\n", "\n", "        # Initial layout setup\n", "        metadata_section.layout.display = 'none'  # Start with direct filename mode\n", "\n", "        # Main container\n", "        container = widgets.VBox([\n", "            widgets.HTML(\"<h3>WebSeed Torrent Creator</h3>\"),\n", "            url_input,\n", "            naming_mode,\n", "            direct_filename,\n", "            file_type,\n", "            metadata_section,\n", "            create_button,\n", "            progress_bar,\n", "            status_label,\n", "            loading_indicator,\n", "            output_area,\n", "            download_area\n", "        ], layout=widgets.Layout(\n", "            width='100%',\n", "            padding='20px',\n", "            border='1px solid #ddd',\n", "            border_radius='5px'\n", "        ))\n", "\n", "        display(container)"], "metadata": {"id": "l9rgfQ7-BITK"}, "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["# Create and display the GUI\n", "creator = MediaTorrentCreator()\n", "creator.create_torrent_gui()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 471, "referenced_widgets": ["f94ff181953845f6bf1cd2790c06482c", "5a643171d7704209ae26d0cc72f35de6", "650a8365a5ea46f9897444f9d29976e3", "1dd1de36da7147ebaf57c3ac26e01e16", "ec31953cf7a54132b4b0a0e6492b0a9c", "3dd745bd3811406e989f7e864ca5a64b", "508b2c8e88a846978796cfdabb21cf30", "cd1eeaec65554cb6b410a3403683103e", "01357233b8444261999638b2821a143b", "6d53d0d9969a4fc7b7711801fb71ba31", "0c17e545386f44ef91972dab303008d8", "e09a60b3518145f4aa8048dcfbb9d10c", "4c1e8353eda0441b932db04f94badd3b", "00ac08cdb7b34dc3b28c815e30e8ec95", "81bbc52a95084b018dfe70b008bbd7fd", "663594b1d1ea43f38a4311fd3a0c258d", "4a6dfa1b025e461a97f9dc1b9bcde46d", "3777b719f6a24a6389b65e4dea9300a7", "2e481d00aa3f427c8556703bbd070a1f", "1c260f8f16514b8da6f65b6285ef50e9", "acb195c4fc23429cbc02f74ffa96f54d", "d490a1616e1549ea9fb34f35b485e49b", "7ccdf13407dc4dabba45164021d0073c", "4c8961f45fd14de492894bcd29e8c339", "53eb6abee62f49499cd0671ca53a5509", "5f97dc511d5a49cca83c8810e272abc2", "ffe7b64a9d714c04949a20d7cecd84c2", "09bbd562f7134a57a6d56a36d41daa23", "5b5d4db33b4d44088fff7f91127b23e2", "152c1e9ef01b4c078e2ac9a2a450b89f", "b3f4caabb5a74121b528af18f57c204b", "c959959fb86646e399ce8d2069851a9a", "33e3feb4cb02417d9e6f40cc1a3881c1", "b84518394a414c2787290e99fe12ca49", "992eabf3533c471b94b3446850f50b11", "f6297debc67c4b30b8ff914fb54a5fea", "b885310e230a404387f33da122b3bc36", "09facb7c405349da9616901d286c600e", "288fd7c5bfa4406cbf3f5fa21e0ce489", "73bf0b41de5d454aafb7f4c62121ca8e", "dde9cf0d1b124d908445d82a5d9dd162", "884b732934394ce5829497feef42fed7", "f76ba60796054bffa5968f21ab23d278", "aae933c5c49c490fad99255389921ad8", "34b47e7eb5154ce58c39f4bcc9a57b84", "bdb82d4baf364284935f0e4029ce11e3", "8baf8d27fb7d4c6894cdf7a6ed1b7a2b", "a3fd88efaac44ad484c2e1d692e21393", "3e4f975300d547bfb0d8bbe3bf704c5d", "cf65722c7f3347d6aa12db29f491fc15", "d3dfcdc9098a4213b26abdc6d5fcdb6b", "c006b6128f92459d9915dca83284af95", "3e651ee6da54499c96aa85995de2cc84", "1735e1b5bcee4ecc983a75152db6edd0", "7a89e13edc9944ca98cd0f432a59c919", "bfd2b708706746c0931171a9afbb45e2", "a8c3d93c1a7e4f09950fc85b916f1503", "ba7dda4bb6f5478d8d1bc8b4327edd1e", "b9eb9edf5d614135aa46ca31fb52ac42", "929bc0d405a541d589ec20c4517f0972", "1bce3a707ab64cb492a7d529e66b804c", "1337c90ee0f4498680abff41da6544c1", "7806daf8d7dc4bbfa96a53d352612d09", "3f07d3b11eb44c20be3fa01c97affb4e", "61642aa8974444e18ce25482ae9a0e5e", "c6e32f9c079e4ff59af071d4de1d309c", "76a6655a459f480a86abda8299000fb2", "c30d9fdff73745ab9d453ea2ccf5c857", "c638b489b6ee4cbcbd833d46f8af8401", "b1e663dd4a1a4668bcc1f083fecb6a40", "d2ed8e7b0c864caa84c87dd34783cfcd", "dc7355250fb3489197d9f210023061be", "a5459264ce2444758b770c5d2323250a", "cef3d4af029144ba94e009af4a675550", "8d87b2887db34c5cbe0122a1fcaf085f", "547474f223c94b888e8e30c666f6371c", "ed2a9f01369a4f55be2208e2d2a0f1a8", "7a6159e116fa43ebbe3c4cb1563ef875", "177c0115fffb4192bc547c595823db1a", "65092a26f43645279b4fd8fb6664e47c", "187003d9379448109a7127ea88f52ec5"]}, "id": "pTos-QBAYN7E", "outputId": "eac16872-98b6-4933-8fd9-bbfb04e9226b"}, "outputs": [], "execution_count": null}]}