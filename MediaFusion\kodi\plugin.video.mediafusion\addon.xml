<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<addon id="plugin.video.mediafusion" version="4.3.33" name="MediaFusion" provider-name="<PERSON>">
    <requires>
        <import addon="xbmc.python" version="3.0.0"/>
        <import addon="xbmc.metadata" version="2.1.0"/>
        <import addon="script.module.requests" version="2.31.0" />
        <import addon="inputstream.ffmpegdirect" version="20.5.0" />
        <import addon="plugin.video.elementum" optional="true" />
    </requires>
    <extension point="xbmc.python.pluginsource" library="main.py">
        <provides>video</provides>
    </extension>
    <extension point="xbmc.addon.metadata">
        <summary lang="en_GB">MediaFusion addon for Kodi</summary>
        <description lang="en_GB">A Kodi addon to provide MediaFusion functionalities similar to the Stremio addon.</description>
        <platform>all</platform>
        <assets>
            <icon>resources/mediafusion_logo.png</icon>
            <fanart>resources/mediafusion_logo.png</fanart>
        </assets>
    </extension>
</addon>
