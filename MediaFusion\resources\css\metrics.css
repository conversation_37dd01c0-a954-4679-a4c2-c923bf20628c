body, html {
    margin: 0;
    padding: 0;
    font-family: 'OpenSans', Arial, sans-serif;
    font-weight: 300;
    color: white;
    width: 100%;
    height: 100%;
    background-image: url('/static/images/background.jpg');
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    overflow-y: auto;
}

h4 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid rgba(74, 71, 163, 0.5);
    padding-bottom: 0.5rem;
}

.config-container {
    margin: 2% auto;
    padding: 2%;
    background: rgba(0, 0, 0, 0.85);
    border-radius: 10px;
    max-height: 95vh;
    overflow-y: auto;
    transition: padding 0.3s ease;
}

.torrent-sources-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    /* Increase padding-bottom for taller chart */
    padding-bottom: 80%;  /* Increased from 50% to give more vertical space */
}

.torrent-sources-wrapper canvas {
    position: absolute;
    width: 100% !important;
    height: 100% !important;
}

.chart-section {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.chart-section:hover {
    transform: translateY(-5px);
}

.week-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin: 20px 0;
    padding: 10px;
    background: rgba(74, 71, 163, 0.2);
    border-radius: 8px;
}

.week-selector button {
    background: #4a47a3;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 130px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.week-selector button:hover {
    background: #5b58b4;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.week-selector button:active {
    transform: translateY(0);
}

.week-selector input[type="date"] {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 7px 14px;
    border-radius: 6px;
    font-size: 0.9rem;
    min-width: 150px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.week-selector input[type="date"]:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.week-selector input[type="date"]:focus {
    outline: none;
    border-color: #4a47a3;
    background: rgba(255, 255, 255, 0.2);
}

/* Style the calendar icon */
.week-selector input[type="date"]::-webkit-calendar-picker-indicator {
    filter: invert(1);
    opacity: 0.7;
    cursor: pointer;
}

.week-selector input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}

.date-info {
    background: rgba(74, 71, 163, 0.4);
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    text-align: center;
    font-size: 1rem;
    margin: 15px 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-wrapper {
    position: relative;
    width: 100%;
    height: 400px;
    margin: 20px 0;
}

.chart-wrapper canvas {
    position: absolute;
    width: 100% !important;
    height: 100% !important;
}

.chart-container {
    padding: 1rem;
    background: rgba(0, 0, 0, 0.37);
    border-radius: 8px;
    transition: margin-bottom 0.3s ease, padding 0.3s ease;
    position: relative;
}

.logo {
    max-width: 300px;
    display: block;
    margin: 0 auto 1.5rem;
}

.card {
    background: linear-gradient(145deg, #4a47a3, #2d2a6a);
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card-text {
    font-size: 1.5rem;
    font-weight: bold;
}

.skeleton-loader {
    width: 100%;
    height: 20vh; /* Dynamic height based on viewport */
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 8px;
    margin-bottom: 1rem;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.card-scheduler {
    background: rgba(0, 0, 0, 0.37);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    transition: background 0.3s ease;
}

.card-scheduler.enabled {
    background: #4a47a3;
    border-left: 5px solid #fff;
}

.card-scheduler.disabled {
    background: #808080;
    border-left: 5px solid #fff;
}

.card-scheduler.log-info {
    border-left: 5px solid #4caf50; /* Green */
}

.card-scheduler.log-warning {
    border-left: 5px solid #ffeb3b; /* Yellow */
}

.card-scheduler.log-error {
    border-left: 5px solid #f44336; /* Red */
}

.scheduler-section {
    margin-bottom: 30px;
}

.scheduler-section h5 {
    margin-bottom: 15px;
}

.section-divider {
    border: 0;
    height: 2px;
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(74, 71, 163, 0.75), rgba(0, 0, 0, 0));
    margin-bottom: 1rem;
}

.btn, button {
    border: none;
    outline: none;
    color: white;
    background: #4a47a3;
    padding: 0.8rem 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    text-align: center;
    font-size: 1rem;
    font-weight: 300;
    cursor: pointer;
    opacity: 0.9;
    transition: all 0.3s ease;
}

.btn:hover, button:hover {
    opacity: 1;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.form-check-input:checked + .form-check-label {
    background: #4a47a3;
    border-color: #4a47a3;
    color: white;
    position: relative;
}

.large-number {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0;
}


@media (max-width: 768px) {
    .chart-wrapper {
        height: 300px;
    }

    .chart-section {
        padding: 15px;
        margin-bottom: 20px;
    }

    .card-text {
        font-size: 1.2rem;
    }
    .config-container {
        padding: 10px;
    }

    .chart-container {
        margin-bottom: 15px;
        padding: 10px;
    }

    .week-selector {
        flex-direction: column;
        gap: 8px;
    }

    .week-selector button,
    .week-selector input[type="date"] {
        width: 100%;
        min-width: unset;
    }

    h3 {
        font-size: 1.2rem;
    }

    .torrent-sources-wrapper {
        padding-bottom: 60%;  /* Slightly smaller for larger screens */
    }
}

@media (min-width: 769px) and (max-width: 1200px) {
    .config-container {
        padding: 30px;
    }

    .chart-container {
        margin-bottom: 20px;
        padding: 20px;
    }

    h3 {
        font-size: 1.4rem;
    }
}

@media (min-width: 1201px) {
    .config-container {
        padding: 50px;
    }

    .chart-container {
        margin-bottom: 30px;
        padding: 30px;
    }

    h3 {
        font-size: 1.6rem;
    }
}
