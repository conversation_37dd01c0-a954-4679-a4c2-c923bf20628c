<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Configuration - Stremio Addon</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.css" rel="stylesheet"/>
    <link href="/static/css/styles.css" rel="stylesheet">

</head>

<body>
<div class="container">
    <div class="row">
        <div class="col-10 col-md-11 col-lg-11 config-container">
            <a href="/"><img class="logo" src="{{ logo_url }}"></a>
            <h3 class="text-center mb-4">{{ addon_name }} Addon Configuration</h3>

            <!-- Configuration Form -->
            <form id="configForm" class="mx-5 mx-lg-1 mx-md-3 mx-sm-4">

                <!-- Configuration Mode Switch -->
                <div class="section-container">
                    <h4 class="section-header">Configuration Mode</h4>
                    <hr class="section-divider">

                    <div class="mode-switch">
                        <div class="mode-buttons">
                            <button type="button" class="mode-btn active" id="newbie_mode" onclick="setConfigMode('newbie')">
                                <i class="bi bi-person-heart"></i>
                                Newbie User
                            </button>
                            <button type="button" class="mode-btn" id="pro_mode" onclick="setConfigMode('pro')">
                                <i class="bi bi-person-gear"></i>
                                Pro User
                            </button>
                        </div>
                        <p class="mode-description">
                            Newbie Mode: Quick setup with essential options for new users
                        </p>
                    </div>
                </div>

                <!-- Streaming Provider -->
                <div class="section-container">
                    <h4 class="section-header">Streaming Provider Configuration</h4>
                    <hr class="section-divider">

                    <label for="provider_service">Streaming Provider: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                            title="Choose a streaming provider to integrate with the addon. Each has unique features and capabilities."></span></label>

                    {% set providers = [
                        {
                            "value": "",
                            "label": "Direct Torrent (P2P)",
                            "type": "Free",
                            "disabled_key": "p2p"
                        },
                        {
                            "value": "pikpak",
                            "label": "PikPak",
                            "type": "Free Quota / Premium"
                        },
                        {
                            "value": "seedr",
                            "label": "Seedr.cc",
                            "type": "Free Quota / Premium"
                        },
                        {
                            "value": "offcloud",
                            "label": "OffCloud",
                            "type": "Free Quota / Premium"
                        },
                        {
                            "value": "torbox",
                            "label": "Torbox",
                            "type": "Free Quota / Premium"
                        },
                        {
                            "value": "realdebrid",
                            "label": "Real-Debrid",
                            "type": "Premium"
                        },
                        {
                            "value": "debridlink",
                            "label": "Debrid-Link",
                            "type": "Premium"
                        },
                        {
                            "value": "premiumize",
                            "label": "Premiumize",
                            "type": "Premium"
                        },
                        {
                            "value": "alldebrid",
                            "label": "AllDebrid",
                            "type": "Premium"
                        },
                        {
                            "value": "easydebrid",
                            "label": "EasyDebrid",
                            "type": "Premium"
                        },
                        {
                            "value": "qbittorrent",
                            "label": "qBittorrent - WebDav",
                            "type": "Free/Premium"
                        },
                        {
                            "value": "stremthru",
                            "label": "StremThru",
                            "type": "Interface"
                        }
                    ] %}
                    <select class="form-control" name="provider_service" id="provider_service" onchange="updateProviderFields(true)">
                        {% for provider in providers %}
                            {% set disabled_key = provider.disabled_key or provider.value %}
                            {% if disabled_key not in disabled_providers %}
                                <option
                                        value="{{ provider.value }}"
                                        {% if (not provider.value and not user_data.streaming_provider.service) or
                    (provider.value and user_data.streaming_provider.service == provider.value) %}
                                        selected
                                        {% endif %}
                                >
                                    {{ provider.label }} ({{ provider.type }})
                                </option>
                            {% endif %}
                        {% endfor %}
                    </select>

                    <!-- Service URL -->
                    <div id="service_url_section" style="display:none" class="mb-4">
                        <label for="service_url">Service URL:</label>
                        <input class="form-control" type="text" id="service_url" name="service_url" placeholder="https://" aria-label="Service URL"
                               value="{{ user_data.streaming_provider.url if user_data.streaming_provider.url else '' }}">
                        <div class="invalid-feedback">
                            Service URL should be valid.
                        </div>
                    </div>

                    <!-- StremThru Configuration -->
                    <div id="stremthru_config" style="display:none" class="mb-4">
                        <label for="stremthru_store_name">Store Name:</label>

                        {% set stremthru_stores = [
                            {
                                "value": "",
                                "label": "",
                            },
                            {
                                "value": "torbox",
                                "label": "Torbox",
                            },
                            {
                                "value": "realdebrid",
                                "label": "Real-Debrid",
                            },
                            {
                                "value": "debridlink",
                                "label": "Debrid-Link",
                            },
                            {
                                "value": "premiumize",
                                "label": "Premiumize",
                            },
                            {
                                "value": "alldebrid",
                                "label": "AllDebrid",
                            },
                        ] %}
                        <select class="form-control" name="stremthru_store_name" id="stremthru_store_name">
                            {% for store in stremthru_stores %}
                                {% if store.value not in disabled_providers %}
                                    <option
                                            value="{{ store.value }}"
                                            {% if (not store.value and not user_data.streaming_provider.stremthru_store_name) or
                        (store.value and user_data.streaming_provider.stremthru_store_name == store.value) %}
                                            selected
                                            {% endif %}
                                    >
                                        {{ store.label }}
                                    </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Affiliate Signup Links -->
                    <div id="signup_section" style="display:none" class="mb-4">
                        <h6>Don't have an account?</h6>
                        <a id="signup_link" href="#" class="btn btn-primary" target="_blank">Create Account</a>
                    </div>

                    <!-- Credentials Input -->
                    <div id="credentials" style="display:none">
                        <h6>Enter Credentials</h6>
                        <div class="mb-3">
                            <label for="email">Email:</label>
                            <input class="form-control" type="text" id="email" name="email" placeholder="Enter Email" aria-label="Email"
                                   value="{{ user_data.streaming_provider.email if user_data.streaming_provider.email else '' }}">
                            <div class="invalid-feedback">
                                Email is required.
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="password">Password:</label>
                            <div class="input-group">
                                <input class="form-control" type="password" id="password" name="password" placeholder="Enter Password">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <span id="togglePasswordIcon" class="bi bi-eye"></span>
                                </button>
                                <div class="invalid-feedback">
                                    Password is required.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- OAuth Section -->
                    <div id="oauth_section" class="mb-3" style="display:none">
                        <h6>Authorize Addon (Recommended) <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                title="Authorize the addon to access your streaming provider account. This is recommended instead using your private token."></span>
                        </h6>
                        <button type="button" id="oauth_btn" class="btn">Authorize</button>
                        <div id="device_code_section" class="mt-2" style="display:none;">
                            To complete the authorization, follow these steps:
                            <ol>
                                <li>Visit the authorization link: <a id="verification_link" href="#" target="_blank">#</a></li>
                                <li>Enter the device code provided below into the authorization page.</li>
                            </ol>
                            Your device code: <strong><span id="device_code_display"></span></strong>
                        </div>
                    </div>


                    <!-- Token for Providers -->
                    <div id="token_input" class="mb-3" style="display:none">
                        <label for="provider_token" class="mb-3">Token: <span id="provider_token_tooltip" class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                              title="Enter Encoded Token previously generated or Click 'Authorize' to generate a new token or Provide your Private Token."></span></label>
                        <div class="input-group">
                            <input class="form-control" type="text" name="provider_token" id="provider_token" placeholder="Enter Token"
                                   value="{{ user_data.streaming_provider.token if user_data.streaming_provider.token else '' }}">
                            <div class="invalid-feedback">
                                Token is required.
                            </div>
                        </div>
                    </div>

                    <!-- qBittorrent Configuration -->
                    <div id="qbittorrent_config" class="mt-3" style="display:none">
                        <h5 class="mt-3">qBittorrent Configuration</h5>
                        <div class="mb-3">
                            <label for="qbittorrent_url">qBittorrent URL:</label>
                            <input class="form-control" type="text" id="qbittorrent_url" name="qbittorrent_url" placeholder="Enter qBittorrent URL"
                                   value="{{ user_data.streaming_provider.qbittorrent_config.qbittorrent_url if user_data.streaming_provider and user_data.streaming_provider.qbittorrent_config else '' }}">
                        </div>
                        <div class="mb-3">
                            <label for="qbittorrent_username">qBittorrent Username: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                                          title="Enter the qBittorrent username for the qBittorrent server. if Authentication not required, leave it blank."></span></label>
                            <input class="form-control" type="text" id="qbittorrent_username" name="qbittorrent_username" placeholder="Enter Username"
                                   value="{{ user_data.streaming_provider.qbittorrent_config.qbittorrent_username if user_data.streaming_provider.qbittorrent_config else '' }}">
                        </div>
                        <div class="mb-3">
                            <label for="qbittorrent_password">qBittorrent Password: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                                          title="Enter the qBittorrent password for the qBittorrent server. if Authentication not required, leave it blank."></span></label>
                            <div class="input-group">
                                <input class="form-control" type="password" id="qbittorrent_password" name="qbittorrent_password" placeholder="Enter qBittorrent Password">
                                <button class="btn btn-outline-secondary" type="button" id="toggleQbittorrentPassword">
                                    <span id="toggleQbittorrentPasswordIcon" class="bi bi-eye"></span>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="seeding_time_limit">Seeding Time Limit (minutes): <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                                                title="Set the seeding time limit for torrents in minutes."></span></label>
                            <input class="form-control" type="number" id="seeding_time_limit" name="seeding_time_limit" placeholder="Enter Seeding Time Limit"
                                   value="{{ user_data.streaming_provider.qbittorrent_config.seeding_time_limit if user_data.streaming_provider.qbittorrent_config else 1440 }}">
                        </div>
                        <div class="mb-3">
                            <label for="seeding_ratio_limit">Seeding Ratio Limit: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                                        title="Set the seeding ratio limit for torrents."></span></label>
                            <input class="form-control" type="text" id="seeding_ratio_limit" name="seeding_ratio_limit" placeholder="Enter Seeding Ratio Limit"
                                   value="{{ user_data.streaming_provider.qbittorrent_config.seeding_ratio_limit if user_data.streaming_provider.qbittorrent_config else 1.0 }}">
                        </div>
                        <div class="mb-3">
                            <label for="play_video_after_download">Play Video After Download: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                                                    title="Enter the percentage of the video to be downloaded before playing."></span></label>
                            <input class="form-control" type="number" id="play_video_after_download" name="play_video_after_download" placeholder="Enter Play Video After Download"
                                   value="{{ user_data.streaming_provider.qbittorrent_config.play_video_after if user_data.streaming_provider.qbittorrent_config else 100 }}">
                        </div>
                        <div class="mb-3">
                            <label for="category">Category: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                  title="Enter the category for the torrents to be tagged in qBittorrent."></span></label>
                            <input class="form-control" type="text" id="category" name="category"
                                   value="{{ user_data.streaming_provider.qbittorrent_config.category if user_data.streaming_provider.qbittorrent_config else 'MediaFusion' }}">
                        </div>

                        <!-- WebDAV Configuration -->
                        <h5 class="mt-3">WebDAV Configuration</h5>
                        <div class="mb-3">
                            <label for="webdav_url">WebDAV URL: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                      title="Enter the WebDAV URL for the qBittorrent WebDAV server with the format: http{s}://host/path."></span></label>
                            <input class="form-control" type="text" id="webdav_url" name="webdav_url" placeholder="Enter WebDAV URL"
                                   value="{{ user_data.streaming_provider.qbittorrent_config.webdav_url if user_data.streaming_provider.qbittorrent_config else '' }}">
                        </div>
                        <div class="mb-3">
                            <label for="webdav_username">WebDAV Username: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                                title="Enter the WebDAV username for the WebDAV server. if Authentication not required, leave it blank."></span></label>
                            <input class="form-control" type="text" id="webdav_username" name="webdav_username" placeholder="Enter WebDAV Username"
                                   value="{{ user_data.streaming_provider.qbittorrent_config.webdav_username if user_data.streaming_provider.qbittorrent_config else '' }}">
                        </div>
                        <div class="mb-3">
                            <label for="webdav_password">WebDAV Password: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                                title="Enter the WebDAV password for the WebDAV server. if Authentication not required, leave it blank."></span></label>
                            <div class="input-group">
                                <input class="form-control" type="password" id="webdav_password" name="webdav_password" placeholder="Enter WebDAV Password">
                                <button class="btn btn-outline-secondary" type="button" id="toggleWebdavPassword">
                                    <span id="toggleWebdavPasswordIcon" class="bi bi-eye"></span>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="webdav_downloads_path">WebDAV Downloads Path: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                                            title="Enter the path from the root of the WebDAV server to the folder where qBittorrent saves downloads. This path is used to locate and stream downloads. If your qBittorrent downloads are saved directly in the root directory of the WebDAV server, leave this as '/'."></span></label>
                            <input class="form-control" type="text" id="webdav_downloads_path" name="webdav_downloads_path" placeholder="Enter WebDAV Downloads Path"
                                   value="{{ user_data.streaming_provider.qbittorrent_config.webdav_downloads_path if user_data.streaming_provider.qbittorrent_config else '/' }}">
                        </div>
                    </div>

                    <!-- Streaming Provider Options -->
                    <div id="streaming_provider_options" style="display:none" class="mb-3">
                        <h6>Streaming Provider Options:</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" name="enable_watchlist"
                                   id="enable_watchlist" {% if user_data.streaming_provider and user_data.streaming_provider.enable_watchlist_catalogs %}checked{% endif %}>
                            <label class="form-check-label" for="enable_watchlist" id="watchlist_label">
                                Enable Watchlist Catalogs
                            </label>
                        </div>
                        <div class="form-check mb-2 pro-mode-section" style="display: none;">
                            <input class="form-check-input" type="checkbox" name="download_via_browser"
                                   id="download_via_browser" {% if user_data.streaming_provider and user_data.streaming_provider.download_via_browser %}checked{% endif %}>
                            <label class="form-check-label" for="download_via_browser">
                                Enable Download via Browser <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                               title="Show a download option in Stremio & Kodi to access torrent streams via web browser."></i>
                            </label>
                        </div>
                        <div class="form-check mb-2 pro-mode-section" style="display: none;">
                            <input class="form-check-input" type="checkbox" name="only_show_cached_streams"
                                   id="only_show_cached_streams" {% if user_data.streaming_provider and user_data.streaming_provider.only_show_cached_streams %}checked{% endif %}>
                            <label class="form-check-label" for="only_show_cached_streams">
                                Only Show Cached Streams
                            </label>
                        </div>
                    </div>

                </div>

                <!-- Catalog Configuration -->
                <div class="section-container pro-mode-section" style="display: none;">
                    <h4 class="section-header">Catalog Configuration</h4>
                    <hr class="section-divider">

                    <div class="mb-3">
                        <h6>Select & Arrange Catalogs: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                             title="Select and arrange the catalogs that you want to display in Stremio."></span></h6>
                        <div id="catalogs" class="row">
                            {% for catalog in catalogs %}
                                <div class="col-12 col-md-6 col-lg-4 draggable-catalog" data-id="{{ catalog[0] }}">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="selected_catalogs" value="{{ catalog[0] }}" id="{{ catalog[0] }}" {% if catalog[0] in
                                           user_data.selected_catalogs %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ catalog[0] }}">
                                            <span class="label-text">{{ catalog[1] }}</span>
                                        </label>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Enable Catalogs Checkbox -->
                    <div class="mb-3">
                        <h6>Enable Catalogs:</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="enable_catalogs"
                                   id="enable_catalogs" {% if user_data.enable_catalogs %}checked{% endif %}>
                            <label class="form-check-label" for="enable_catalogs">
                                Show Catalogs in Stremio & Kodi <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                      title="Toggle to add or hide catalogs in Stremio. This is perfect for when you want to install multiple addons without creating duplicate catalog content and only want to display streams."></span>
                            </label>
                        </div>
                    </div>
                    <!-- Enable MetaData Response Checkbox -->
                    <div class="mb-3">
                        <h6>Enable IMDb Title Meta Data Response:</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="enable_imdb_metadata"
                                   id="enable_imdb_metadata" {% if user_data.enable_imdb_metadata %}checked{% endif %}>
                            <label class="form-check-label" for="enable_imdb_metadata">
                                Enable IMDb Meta Data Response
                                <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                      title="Toggle to enable or disable IMDb title metadata response.
                                      Stremio user prefer to only use cinemeta Addon for metadata and disable this. For the Kodi users, this option need to be enabled."></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Parental Guides Configuration -->
                <div class="section-container pro-mode-section" style="display: none;">
                    <h4 class="section-header">Parental Guides</h4>
                    <hr class="section-divider">

                    <div class="mb-3">
                        <h6>Select Certification Levels To Not Display: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                              title="Select the certification levels that you prefer not to display in Stremio."></span></h6>
                        <div class="row">
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="certification_filter" value="Disable" id="certification_disable"
                                           {% if "Disable" in user_data.certification_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="certification_disable">Disable</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="certification_filter" value="Unknown" id="certification_unknown"
                                           {% if "Unknown" in user_data.certification_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="certification_unknown">Unknown</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="certification_filter" value="All Ages" id="certification_all_ages"
                                           {% if "All Ages" in user_data.certification_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="certification_all_ages">All Ages</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="certification_filter" value="Children" id="certification_children"
                                           {% if "Children" in user_data.certification_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="certification_children">Children</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="certification_filter" value="Parental Guidance" id="certification_pg"
                                           {% if "Parental Guidance" in user_data.certification_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="certification_pg">Parental Guidance</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="certification_filter" value="Teens" id="certification_teens"
                                           {% if "Teens" in user_data.certification_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="certification_teens">Teens</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="certification_filter" value="Adults" id="certification_adults"
                                           {% if "Adults" in user_data.certification_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="certification_adults">Adults</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="certification_filter" value="Adults+" id="certification_adults+"
                                           {% if "Adults+" in user_data.certification_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="certification_adults+">Adults+</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>Select Nudity Filter Levels To Not Display: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                              title="Select the nudity filter levels that you prefer not to display in Stremio."></span></h6>
                        <div class="row">
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="nudity_filter" value="Disable" id="nudity_disable"
                                           {% if "Disable" in user_data.nudity_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="nudity_disable">Disable</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="nudity_filter" value="Unknown" id="nudity_unknown"
                                           {% if "Unknown" in user_data.nudity_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="nudity_unknown">Unknown</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="nudity_filter" value="None" id="nudity_none"
                                           {% if "None" in user_data.nudity_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="nudity_none">None</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="nudity_filter" value="Mild" id="nudity_mild"
                                           {% if "Mild" in user_data.nudity_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="nudity_mild">Mild</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="nudity_filter" value="Moderate" id="nudity_moderate"
                                           {% if "Moderate" in user_data.nudity_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="nudity_moderate">Moderate</label>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 col-lg-4">
                                <div class="form-check">
                                    <input class="form-check-input parental-guide-checkbox" type="checkbox" name="nudity_filter" value="Severe" id="nudity_severe"
                                           {% if "Severe" in user_data.nudity_filter %}checked{% endif %}>
                                    <label class="form-check-label" for="nudity_severe">Severe</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Streaming Filter Configuration -->
                <div class="section-container">
                    <h4 class="section-header">Streaming Preferences <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                           title="Customize how streams are sorted, limit results, and choose torrent display options to tailor your streaming experience in Stremio."></span>
                    </h4>
                    <hr class="section-divider">

                    <!-- Select Streaming Resolutions -->
                    <div class="mb-3">
                        <h6>Select Streaming Resolutions: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                title="Select the streaming resolutions that you prefer. The addon will filter these when fetching streams."></span></h6>
                        <div class="row">
                            {% for resolution in resolutions %}
                                <div class="col-12 col-md-6 col-lg-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="selected_resolutions"
                                               value="{{ resolution if resolution else '' }}"
                                               id="resolution_{{ resolution if resolution else 'unknown' }}"
                                               {% if resolution in user_data.selected_resolutions %}checked{% endif %}>
                                        <label class="form-check-label" for="resolution_{{ resolution if resolution else 'unknown' }}">
                                            {{ resolution if resolution else 'Unknown' }}
                                        </label>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Quality Filter -->
                    <div class="mb-3">
                        <h6>Select Quality Filter: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                         title="Select the quality levels to filter streams."></span></h6>
                        <div class="row">
                            {% for group, qualities in quality_groups.items() %}
                                <div class="col-12 col-md-6 col-lg-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="quality_filter" value="{{ group }}" id="quality_{{ group }}"
                                               {% if group in user_data.quality_filter %}checked{% endif %}>
                                        <label class="form-check-label" for="quality_{{ group }}">
                                            {{ group }} <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                              title="Contains: {{ qualities }}"></span>
                                        </label>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- File Size Range Filter -->
                    <div class="mb-3">
                        <h6>Set File Size Filter: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                        title="Select the file size range for the streams. Slide to the end for no limit."></span></h6>
                        <!-- Slider for the file size -->
                        <input type="range" class="form-range" id="max_size_slider" name="size_slider" min="0" max="43000000000"
                               value="{{ user_data.max_size if user_data.max_size < 43000000000 else 43000000000 }}" step="100000000">
                        <label for="max_size_slider">Max File Size:</label>
                        <span id="max_size_output">Unlimited</span>
                    </div>

                    <!-- Stream Sorting Priority -->
                    <div class="mb-3 pro-mode-section" style="display: none;">
                        <h6>Select & Arrange Sorting Priority:
                            <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                  title="Select and arrange the sorting options. Click to toggle sort direction."></span>
                        </h6>

                        <div id="streamSortOrder" class="row g-3">
                            {% set tooltip_text = {
                                'language': 'Sort by preferred languages first|Sort by least preferred languages first',
                                'cached': 'Show cached results first|Show uncached results first',
                                'resolution': 'Highest resolution first|Lowest resolution first',
                                'quality': 'Best quality first|Lower quality first',
                                'size': 'Largest size first|Smallest size first',
                                'seeders': 'Most seeders first|Fewest seeders first',
                                'created_at': 'Newest first|Oldest first'
                            } %}
                            {% for sorting_option in sorting_options %}
                                {% set active_sort = namespace(found=false, direction='desc') %}
                                {% for sort in user_data.torrent_sorting_priority %}
                                    {% if sort.key == sorting_option.key %}
                                        {% set active_sort.found = true %}
                                        {% set active_sort.direction = sort.direction %}
                                    {% endif %}
                                {% endfor %}

                                <div class="col-12 col-md-6 col-lg-4 sortable-list" data-id="{{ sorting_option.key }}">
                                    <div class="d-flex align-items-center border rounded p-3 w-100 sort-item {% if active_sort.found %}active{% endif %}">
                                        <div class="form-check flex-grow-1">
                                            <input class="form-check-input" type="checkbox" name="selected_sorting_options"
                                                   value="{{ sorting_option.key }}"
                                                   id="sorting_{{ sorting_option.key }}"
                                                   {% if active_sort.found %}checked{% endif %}>
                                            <label class="form-check-label" for="sorting_{{ sorting_option.key }}">
                                                {{ sorting_option.key.replace('_', ' ').title() }}
                                            </label>
                                        </div>

                                        <!-- Direction toggle button with more descriptive text -->
                                        <button type="button"
                                                class="btn btn-outline-primary sort-direction-toggle px-3"
                                                data-sort-id="{{ sorting_option.key }}"
                                                data-bs-toggle="tooltip"
                                                title="{{ tooltip_text[sorting_option.key].split('|')[1 if active_sort.direction == 'asc' else 0] }}"
                                                sorting_info="{{ tooltip_text[sorting_option.key] }}"
                                                {% if not active_sort.found %}disabled{% endif %}>
                        <span class="sort-text">
                            {% if active_sort.direction == 'asc' %}
                                <i class="bi bi-sort-up-alt"></i>
                                <span class="d-sm-inline ms-1">{{ tooltip_text[sorting_option.key].split('|')[1] }}</span>
                            {% else %}
                                <i class="bi bi-sort-down"></i>
                                <span class="d-sm-inline ms-1">{{ tooltip_text[sorting_option.key].split('|')[0] }}</span>
                            {% endif %}
                        </span>
                                        </button>

                                        <input type="hidden"
                                               name="sorting_direction_{{ sorting_option.key }}"
                                               id="direction_{{ sorting_option.key }}"
                                               value="{{ active_sort.direction }}">
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>


                    <!-- Preferred Language Sorting -->
                    <div class="mb-3" id="languageSortSection" style="display:none;">
                        <h6>Filter & Arrange Preferred Languages: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                        title="Select and arrange your preferred languages for streams."></span></h6>
                        <div id="languageSortOrder" class="row">
                            {% for language in sorted_languages %}
                                <div class="col-12 col-md-6 col-lg-4 draggable-language">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="selected_languages" value="{{ language if language else '' }}" id="language_{{ language }}"
                                               {% if language in user_data.language_sorting %}checked{% endif %}>
                                        <label class="form-check-label" for="language_{{ language }}">
                                            {{ language if language else 'Unknown' }}
                                        </label>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Maximum Streams Result per Resolution Configuration -->
                    <div class="mb-3 pro-mode-section" style="display: none;">
                        <label for="maxStreamsPerResolution">Max Streams Per Resolution: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                                               title="Enter the maximum number of streams per resolution to display in Stremio."></span></label>
                        <input type="number" class="form-control" id="maxStreamsPerResolution" name="maxStreamsPerResolution" min="1" placeholder="Enter maximum streams per resolution"
                               value="{{ user_data.max_streams_per_resolution }}">
                        <div class="invalid-feedback">
                            Please enter a valid number.
                        </div>
                    </div>

                    <!-- Torrent Information Display Configuration -->
                    <div class="mb-3 pro-mode-section" style="display: none;">
                        <h6>Torrent Stream Display option: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                 title="Choose how you want to display the torrent information in Stremio."></span></h6>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="torrentDisplayOption" id="showParsedTorrentData" value="parsedData"
                                   {% if not user_data.show_full_torrent_name %}checked{% endif %}>
                            <label class="form-check-label" for="showParsedTorrentData">
                                Show Parsed Data (Quality, Resolution, Codec, Audio.)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="torrentDisplayOption" id="showTorrentName" value="fullName" {% if user_data.show_full_torrent_name %}checked{% endif %}>
                            <label class="form-check-label" for="showTorrentName">
                                Show Torrent Full Name
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="showTorrentLanguageFlag" id="showTorrentLanguageFlag" {% if user_data.show_language_country_flag %}checked{% endif %}>
                            <label class="form-check-label" for="showTorrentLanguageFlag">
                                Show Language Country Flag
                            </label>
                        </div>
                    </div>

                    <!-- Enable / Disable live_search_streams -->
                    <div class="mb-3 pro-mode-section" style="display: none;">
                        <h6>Live Search Streams: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                       title="Opt-in to enable live search based on search timeout period. Otherwise, the stream result will be based on the cached data."></span>
                        </h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="liveSearchStreams" id="liveSearchStreams"
                                   {% if user_data.live_search_streams %}checked{% endif %}>
                            <label class="form-check-label" for="liveSearchStreams">
                                Enable on-demand search for movies & series streams
                            </label>
                        </div>
                    </div>

                    <!-- Enable / Disable contribution_streams -->
                    <div class="mb-3 pro-mode-section" style="display: none;">
                        <h6>Contribution Streams: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                        title="This feature allows you to show optional stream to add your own stream data into MediaFusion."></span>
                        </h6>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="contributionStreams" id="contributionStreams"
                                   {% if user_data.contribution_streams %}checked{% endif %}>
                            <label class="form-check-label" for="contributionStreams">
                                Enable Contribution Streams
                            </label>
                        </div>
                    </div>
                </div>

                <div class="section-container pro-mode-section" style="display: none;">
                    <h4 class="section-header">External Services Configuration</h4>
                    <hr>

                    <div class="mb-3">
                        <h6>MediaFlow Configuration</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="enable_mediaflow" name="enable_mediaflow"
                                   {% if user_data.mediaflow_config %}checked{% endif %}>
                            <label class="form-check-label" for="enable_mediaflow">
                                Enable MediaFlow
                                <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                   title="Enable to use MediaFlow for handling various stream types and routing."></i>
                            </label>
                        </div>
                        <div id="mediaflow_config" style="display: {% if user_data.mediaflow_config %}block{% else %}none{% endif %};">
                            <div class="mb-2">
                                <a href="https://github.com/mhdzumair/mediaflow-proxy?tab=readme-ov-file#mediaflow-proxy" target="_blank" rel="noopener">
                                    MediaFlow Setup Guide
                                </a>
                            </div>
                            <div class="mb-2">
                                <label for="mediaflow_proxy_url">MediaFlow Proxy URL:</label>
                                <input type="text" class="form-control" id="mediaflow_proxy_url" name="mediaflow_proxy_url"
                                       placeholder="https://your-mediaflow-proxy-url.com or http://127.0.0.1:8888"
                                       value="{{ user_data.mediaflow_config.proxy_url if user_data.mediaflow_config else '' }}">
                                <div class="invalid-feedback">
                                    Please enter a valid MediaFlow Proxy URL.
                                </div>
                            </div>
                            <div class="mb-2">
                                <label for="mediaflow_api_password">MediaFlow API Password:</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="mediaflow_api_password" name="mediaflow_api_password"
                                           value="{{ user_data.mediaflow_config.api_password if user_data.mediaflow_config else '' }}">
                                    <button class="btn btn-outline-secondary" type="button" id="toggleMediaFlowPassword">
                                        <i id="toggleMediaFlowPasswordIcon" class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Please enter the MediaFlow API password.
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="mediaflow_public_ip">MediaFlow Public IP (Optional):</label>
                                <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                   title="Configure this only when running MediaFlow locally with a proxy service. Leave empty if MediaFlow is configured locally without a proxy server or if it's hosted on
                                    a remote server."></i>
                                <input type="text" class="form-control" id="mediaflow_public_ip" placeholder="Enter public IP address. (Optional, See tooltip for details)"
                                       value="{{ user_data.mediaflow_config.public_ip if user_data.mediaflow_config else '' }}">
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="proxy_live_streams" name="proxy_live_streams"
                                       {% if user_data.mediaflow_config and user_data.mediaflow_config.proxy_live_streams %}checked{% endif %}>
                                <label class="form-check-label" for="proxy_live_streams">
                                    Proxy Live Streams
                                    <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                       title="Enable to proxy live streams through MediaFlow."></i>
                                </label>
                            </div>
                            <div id="proxy_debrid_streams_section" class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="proxy_debrid_streams" name="proxy_debrid_streams"
                                       {% if user_data.mediaflow_config and user_data.mediaflow_config.proxy_debrid_streams %}checked{% endif %}>
                                <label class="form-check-label" for="proxy_debrid_streams">
                                    Proxy Debrid Streams
                                    <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                       title="Enable to proxy debrid streams through MediaFlow."></i>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>RPDB (RatingPosterDB) Configuration</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="enable_rpdb" name="enable_rpdb"
                                   {% if user_data.rpdb_config %}checked{% endif %}>
                            <label class="form-check-label" for="enable_rpdb">
                                Enable RPDB Posters
                                <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                   title="Enable to use RPDB for rating posters. Poster configurations can be managed in your RPDB account settings."></i>
                            </label>
                        </div>
                        <div id="rpdb_config" style="display: {% if user_data.rpdb_config %}block{% else %}none{% endif %};">
                            <div class="mb-2">
                                <a href="https://ratingposterdb.com/api-key/" target="_blank" rel="noopener">Get RPDB API Key</a>
                                <span class="mx-2">|</span>
                                <a href="https://manager.ratingposterdb.com" target="_blank" rel="noopener">Configure RPDB Posters Settings</a>
                            </div>
                            <p class="text-secondary">
                                <small>RPDB is an optional <b>Freemium</b> third-party service for enhanced rating posters. By default, MediaFusion generates rating posters using IMDb ratings.</small>
                            </p>
                            <div class="mb-2">
                                <label for="rpdb_api_key">RPDB API Key:</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="rpdb_api_key" name="rpdb_api_key"
                                           value="{{ user_data.rpdb_config.api_key if user_data.rpdb_config else '' }}">
                                    <button class="btn btn-outline-secondary" type="button" id="toggleRPDBApiKey">
                                        <i id="toggleRPDBApiKeyIcon" class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Please enter a valid RPDB API Key.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6>MDBList Configuration</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="enable_mdblist" name="enable_mdblist"
                                   {% if user_data.mdblist_config %}checked{% endif %}>
                            <label class="form-check-label" for="enable_mdblist">
                                Enable MDBList Integration
                                <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                   title="Enable to use MDBList for custom movie and TV show lists."></i>
                            </label>
                        </div>
                        <div id="mdblist_config" style="display: {% if user_data.mdblist_config %}block{% else %}none{% endif %};">
                            <!-- API Key Configuration -->
                            <div class="mb-3">
                                <label for="mdblist_api_key">MDBList API Key:</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="mdblist_api_key" name="mdblist_api_key"
                                           value="{{ user_data.mdblist_config.api_key if user_data.mdblist_config else '' }}"
                                           placeholder="Enter your MDBList API key">
                                    <button class="btn btn-outline-secondary" type="button" id="toggleMDBListApiKey">
                                        <i id="toggleMDBListApiKeyIcon" class="bi bi-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-primary" type="button" id="verifyMDBListApiKey">
                                        <i class="bi bi-check-circle"></i> Verify
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Please enter a valid MDBList API Key.
                                </div>
                                <small class="form-text text-secondary">
                                    To get your API key:
                                    <ol>
                                        <li>Sign up/Login to <a href="https://mdblist.com" target="_blank">MDBList</a></li>
                                        <li>Go to <a href="https://mdblist.com/preferences/" target="_blank">API Key Preferences</a></li>
                                        <li>Create a new API key if you don't have one</li>
                                    </ol>
                                </small>
                            </div>

                            <!-- List Management Section -->
                            <div id="mdblist_management" style="display: none">
                                <!-- Tabs for different list sources -->
                                <ul class="nav nav-tabs mb-3" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="my-lists-tab" data-bs-toggle="tab" data-bs-target="#my-lists" type="button" role="tab">
                                            <i class="bi bi-person"></i> My Lists
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="top-lists-tab" data-bs-toggle="tab" data-bs-target="#top-lists" type="button" role="tab">
                                            <i class="bi bi-trophy"></i> Top Lists
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="search-lists-tab" data-bs-toggle="tab" data-bs-target="#search-lists" type="button" role="tab">
                                            <i class="bi bi-search"></i> Search Lists
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="add-list-tab" data-bs-toggle="tab" data-bs-target="#add-list" type="button" role="tab">
                                            <i class="bi bi-plus-circle"></i> Add List
                                        </button>
                                    </li>
                                </ul>

                                <!-- Tab Content -->
                                <div class="tab-content">
                                    <!-- My Lists -->
                                    <div class="tab-pane fade show active" id="my-lists" role="tabpanel">
                                        <div class="my-lists-container">
                                            <div class="text-center" id="my-lists-loading">
                                                <div class="spinner-border" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                            <div id="my-lists-content"></div>
                                        </div>
                                    </div>

                                    <!-- Top Lists -->
                                    <div class="tab-pane fade" id="top-lists" role="tabpanel">
                                        <div class="top-lists-container">
                                            <div class="text-center" id="top-lists-loading">
                                                <div class="spinner-border" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                            <div id="top-lists-content"></div>
                                        </div>
                                    </div>

                                    <!-- Search Lists -->
                                    <div class="tab-pane fade" id="search-lists" role="tabpanel">
                                        <div class="input-group mb-3">
                                            <input type="text" class="form-control" id="list-search-input" placeholder="Search for lists...">
                                            <button class="btn btn-outline-primary" type="button" id="search-lists-btn">
                                                <i class="bi bi-search"></i> Search
                                            </button>
                                        </div>
                                        <div id="search-lists-content"></div>
                                    </div>

                                    <!-- Add List Manually -->
                                    <div class="tab-pane fade" id="add-list" role="tabpanel">
                                        <div class="mb-3">
                                            <label for="list-url-input" class="form-label">MDBList URL or ID</label>
                                            <input type="text" class="form-control" id="list-url-input"
                                                   placeholder="https://mdblist.com/lists/username/list-name or list-id">
                                        </div>
                                        <button type="button" class="btn btn-primary" id="add-list-btn">
                                            <i class="bi bi-plus-circle"></i> Add List
                                        </button>
                                    </div>
                                </div>

                                <!-- Selected Lists -->
                                <div class="mt-4">
                                    <h6>Selected Lists:</h6>
                                    <div id="selected-lists" class="list-group">
                                        <!-- Selected lists will be populated here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                {% if authentication_required %}
                    <!-- API Password Configuration -->
                    <div class="section-container">
                        <h4 class="section-header">API Security Configuration</h4>
                        <hr class="section-divider">

                        <!-- API Password Input -->
                        <div class="mb-3">
                            <label for="api_password">API Password: <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                          title="Enter the API password you configured in the environment variables."></span></label>
                            <div class="input-group">
                                <input class="form-control" type="password" id="api_password" name="api_password" placeholder="Enter API Password">
                                <button class="btn btn-outline-secondary" type="button" id="toggleApiPassword">
                                    <span id="toggleApiPasswordIcon" class="bi bi-eye"></span>
                                </button>
                                <div class="invalid-feedback">
                                    API Password is required.
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}


                <!-- Fallback URL Display - Initially hidden -->
                <div id="fallbackUrlContainer" class="fallback-url-container" style="display: none;">
                    <label for="fallbackUrl">Installation URL <span class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                                                                    title="This is the URL that you can use to install the addon in Stremio."></span></label>
                    <textarea id="fallbackUrl" class="form-control" readonly></textarea>
                    <p>Please manually copy the URL above and paste it in the Stremio search bar. Do not share this URL with unknown persons.</p>
                </div>


                <!-- Submit and Share Buttons -->
                <div class="button-container">
                    <button type="submit" class="btn btn-primary btn-block custom-btn">Install in Stremio</button>
                    <button type="button" id="shareBtn" style="display: none;" class="btn btn-primary btn-block custom-btn">Share Manifest URL</button>
                    <button type="button" id="copyBtn" style="display: none;" class="btn btn-primary btn-block custom-btn">Copy Manifest URL</button>
                    <button type="button" id="kodiSetupBtn" class="btn btn-primary btn-block custom-btn">Setup Kodi Addon</button>
                </div>

                <!-- Kodi Code Input Modal -->
                <div class="modal fade" id="kodiCodeModal" tabindex="-1" aria-labelledby="kodiCodeModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title text-primary-emphasis" id="kodiCodeModalLabel">Enter Kodi Setup Code</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-2">
                                    <a href="https://github.com/mhdzumair/MediaFusion?tab=readme-ov-file#kodi-add-on-installation" target="_blank" rel="noopener">
                                        Kodi Addon Setup Guide
                                    </a>
                                </div>
                                <input type="text" id="kodiCodeInput" class="form-control" placeholder="Enter 6-digit code" maxlength="6" value="{{ kodi_code or '' }}">
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" id="submitKodiCode">Submit</button>
                            </div>
                        </div>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>
<div id="loadingWidget" class="loading-overlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5>Please Wait</h5>
        <p id="loadingMessage">Processing your configuration...</p>
    </div>
</div>


<!-- List Item Template -->
<template id="list-item-template">
    <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
        <div class="me-auto">
            <h6 class="mb-1 list-title"></h6>
            <div class="text-secondary list-details">
                <span class="item-count"></span>
                <span class="like-count badge bg-success"></span>
                <span class="media-type badge bg-primary"></span>
                <span class="list-owner badge bg-info"></span>
            </div>
        </div>
        <div class="btn-group">
            <a href="#" class="btn btn-sm btn-outline-primary list-link" target="_blank">
                <i class="bi bi-eye"></i>
            </a>
            <button type="button" class="btn btn-sm btn-outline-primary add-list-btn">
                <i class="bi bi-plus-circle"></i> Add
            </button>
        </div>
    </div>
</template>

<!-- Selected List Item Template -->
<template id="selected-list-item-template">
    <div class="list-group-item d-flex justify-content-between align-items-center">
        <div class="me-auto">
            <h6 class="mb-1 list-title"></h6>
            <div class="text-secondary">
                <span class="media-type badge bg-primary"></span>
                <span class="use-filters badge bg-secondary"></span>
                <span class="sort-info badge bg-info"></span>
            </div>
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-outline-primary edit-btn">
                <i class="bi bi-pencil"></i>
            </button>
            <button type="button" class="btn btn-sm btn-outline-danger remove-btn">
                <i class="bi bi-trash"></i>
            </button>
        </div>
    </div>
</template>

<!-- Edit List Modal -->
<div class="modal fade" id="editListModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit List</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="edit-list-id">
                <input type="hidden" id="edit-list-original-id">
                <div class="mb-3">
                    <label for="edit-list-title" class="form-label">List Title</label>
                    <input type="text" class="form-control" id="edit-list-title">
                </div>
                <div class="mb-3">
                    <label class="form-label">Media Type</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="edit-list-type" id="edit-list-type-movie" value="movie">
                        <label class="form-check-label" for="edit-list-type-movie">Movies</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="edit-list-type" id="edit-list-type-show" value="series">
                        <label class="form-check-label" for="edit-list-type-show">Series</label>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Sorting Options</label>
                    <div class="row">
                        <div class="col-8">
                            <select class="form-select" id="edit-list-sort">
                                <option value="rank">Rank (Default)</option>
                                <option value="score">Score</option>
                                <option value="usort">User Sort</option>
                                <option value="score_average">Score Average</option>
                                <option value="released">Release Date</option>
                                <option value="releasedigital">Digital Release Date</option>
                                <option value="imdbrating">IMDb Rating</option>
                                <option value="imdbvotes">IMDb Votes</option>
                                <option value="last_air_date">Last Air Date</option>
                                <option value="imdbpopular">IMDb Popular</option>
                                <option value="tmdbpopular">TMDB Popular</option>
                                <option value="rogerebert">Roger Ebert Rating</option>
                                <option value="rtomatoes">Rotten Tomatoes</option>
                                <option value="rtaudience">RT Audience Score</option>
                                <option value="metacritic">Metacritic</option>
                                <option value="myanimelist">MyAnimeList</option>
                                <option value="letterrating">Letterboxd Rating</option>
                                <option value="lettervotes">Letterboxd Votes</option>
                                <option value="budget">Budget</option>
                                <option value="revenue">Revenue</option>
                                <option value="runtime">Runtime</option>
                                <option value="title">Title</option>
                                <option value="added">Date Added</option>
                                <option value="random">Random</option>
                            </select>
                        </div>
                        <div class="col-4">
                            <select class="form-select" id="edit-list-order">
                                <option value="desc">Descending</option>
                                <option value="asc">Ascending</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Catalog Filtering</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="edit-list-use-filters">
                        <label class="form-check-label" for="edit-list-use-filters">
                            Apply MediaFusion Filters
                            <i class="bi bi-question-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                               title="When enabled, this list will only show items that match your configured filters (nudity, certification, etc.) and have available streams."></i>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="save-list-edit">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<input type="hidden" id="configured_fields" value='{{ configured_fields|tojson|safe }}'>
<input type="hidden" id="existing_config" value="{{ secret_str if secret_str else '' }}">
<input type="hidden" id="mdblist_configured_lists" value='{{ mdblist_configured_lists|tojson|safe }}'>

<!-- JS for Bootstrap and form validation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.6/Sortable.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"></script>
<script src="/static/js/mdblist.js"></script>
<script src="/static/js/config_script.js"></script>
</body>

</html>

