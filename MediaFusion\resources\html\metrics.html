<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ addon_name }} - Stremio Addon Metrics Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/metrics.css" rel="stylesheet">
    <link rel="shortcut icon" href="{{ logo_url }}" type="image/x-icon">
</head>

<body>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-10 col-lg-10 col-md-8 col-sm-10 config-container">
            <a href="/"><img class="logo" src="{{ logo_url }}"></a>
            <h3 class="text-center mb-5"><b>{{ addon_name }} Metrics Dashboard</b></h3>

            <div class="chart-section">
                <h4 class="text-left">Scrapy Scheduler Details</h4>
                <div id="schedulerChartsContainer" class="row">
                    <div class="skeleton-loader" id="schedulerChartsSkeleton"></div>
                </div>
            </div>

            <div class="chart-section">
                <h4 class="text-left">MetaData Counts</h4>
                <div class="chart-wrapper">
                    <canvas id="metadataCountsChart"></canvas>
                    <div class="skeleton-loader" id="metadataCountsSkeleton"></div>
                </div>
            </div>


            <div class="chart-section">
                <h4 class="text-left">{{ addon_name }} Available Torrents</h4>
                <div class="card text-white mb-3">
                    <div class="card-body">
                        <h5 class="card-title">Total Torrents</h5>
                        <p id="totalTorrentsValue" class="card-text"></p>
                    </div>
                    <div class="skeleton-loader" id="totalTorrentsSkeleton"></div>
                </div>
            </div>

            <div class="chart-section">
                <h4 class="text-left">Top 20 Torrents Sources</h4>
                <div class="chart-wrapper">
                    <canvas id="torrentSourcesChart"></canvas>
                    <div class="skeleton-loader" id="torrentSourcesSkeleton"></div>
                </div>
            </div>

            <div class="chart-section">
                <h4 class="text-left">Top 20 Torrents Uploaders</h4>
                <div class="chart-wrapper">
                    <canvas id="torrentUploadersChart"></canvas>
                    <div class="skeleton-loader" id="torrentUploadersSkeleton"></div>
                </div>
            </div>

            <div class="chart-section">
                <h4 class="text-left">Top 20 Contribution Streams Uploaders</h4>
                <div class="week-selector mb-3">
                    <button class="btn btn-outline-light btn-sm" id="prevWeek">Previous Week</button>
                    <input type="date" id="weekSelector" class="form-control mx-2">
                    <button class="btn btn-outline-light btn-sm" id="nextWeek">Next Week</button>
                </div>
                <div class="date-info mb-3" id="weekDateRange"></div>
                <div class="chart-wrapper">
                    <canvas id="weeklyUploadersChart"></canvas>
                    <div class="skeleton-loader" id="weeklyUploadersSkeleton"></div>
                </div>
            </div>

            <div class="chart-section">
                <h4 class="text-left">Debrid Cache Statistics</h4>
                <div class="card text-white">
                    <div class="card-body">
                        <h5 class="card-title">Cached Torrents by Service</h5>
                        <div class="chart-wrapper">
                            <canvas id="debridCacheChart"></canvas>
                            <div class="skeleton-loader" id="debridChartSkeleton"></div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- JS for Bootstrap -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.1/chart.umd.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-plugin-datalabels/2.2.0/chartjs-plugin-datalabels.min.js"></script>
<script src="/static/js/metrics.js"></script>
</body>

</html>
