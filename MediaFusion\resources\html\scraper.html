<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Scraper Controls - {{ addon_name }} Stremio Addon</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.css" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flatpickr/4.6.13/flatpickr.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flatpickr/4.6.13/themes/dark.min.css" rel="stylesheet">
    <link href="/static/css/styles.css" rel="stylesheet">
    <link rel="shortcut icon" href="{{ logo_url }}" type="image/x-icon">
</head>

<body>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-10 col-lg-10 col-md-8 col-sm-10 config-container">
            <a href="/"><img class="logo" src="{{ logo_url }}"></a>
            <h3 class="text-center mb-5"><b>Scraper Controls</b> - Manage Scraping Tasks</h3>

            <!-- Scraper Selection -->
            <div class="section-container">
                <h4 class="section-header">Scraper Selection</h4>
                <hr class="section-divider">
                <div class="mb-3">
                    <label for="scraperSelect" class="form-label">Select Scraper</label>
                    <select class="form-select" id="scraperSelect" onchange="updateFormFields()">
                        <option value="quick_import">Quick Import (Auto-detect)</option>
                        <option value="add_torrent">Add Torrent Data</option>
                        <option value="scrapy">Trigger Scrapy Spider</option>
                        <option value="add_tv_metadata">Add TV Metadata</option>
                        <option value="add_m3u_playlist">Add M3U Playlist</option>
                        <option value="update_imdb_data">Update IMDb Data</option>
                        <option value="block_torrent">Block/Delete Torrent</option>
                        <option value="migrate_id">Migrate MediaFusion ID to IMDb ID</option>
                        <option value="update_images">Update Images</option>
                    </select>
                </div>
            </div>

            <!-- Dynamic Scraper Parameters -->
            <div id="scraperParameters">

                <div id="quickImportParameters" style="display: none;">
                    <div class="section-container">
                        <h4 class="section-header">Quick Import</h4>
                        <hr class="section-divider">

                        <!-- Content Type and Torrent Type Selection -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="quickMetaType" class="form-label">Content Type</label>
                                <select class="form-select" id="quickMetaType" name="quickMetaType" required>
                                    <option value="movie">Movie</option>
                                    <option value="series">Series</option>
                                    <option value="sports">Sports</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="quickTorrentType" class="form-label">Torrent Type</label>
                                <select class="form-select" id="quickTorrentType" name="quickTorrentType">
                                    <option value="public">Public</option>
                                    <option value="semi-private">Semi-Private</option>
                                    <option value="private">Private</option>
                                    <option value="web-seed">Web-Seed</option>
                                </select>
                            </div>
                        </div>

                        <!-- Uploader Information -->
                        <div class="mb-3">
                            <label for="quickUploaderName" class="form-label">Uploader Name (Optional)</label>
                            <input type="text" class="form-control" id="quickUploaderName"
                                   placeholder="Enter your name or leave blank for Anonymous">
                        </div>

                        <!-- Torrent Upload Area -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-subtitle mb-3 text-muted">Recommended: Torrent File Upload</h6>
                                <div class="mb-3">
                                    <label for="quickTorrentFile" class="form-label">Torrent File</label>
                                    <input type="file" class="form-control" id="quickTorrentFile"
                                           name="quickTorrentFile" accept=".torrent"
                                           onchange="toggleInput('quickMagnetLink', this)">
                                    <div class="form-text">Upload a torrent file for better metadata extraction</div>
                                </div>
                            </div>
                        </div>

                        <!-- Magnet Link Alternative -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-subtitle mb-3 text-muted">Alternative: Magnet Link</h6>
                                <div class="mb-3">
                                    <label for="quickMagnetLink" class="form-label">Magnet Link</label>
                                    <input type="text" class="form-control" id="quickMagnetLink"
                                           name="quickMagnetLink" placeholder="magnet:?xt=urn:btih:..."
                                           onchange="toggleInput('quickTorrentFile', this)">
                                    <div class="form-text text-warning">
                                        <i class="bi bi-exclamation-triangle"></i>
                                        Note: Magnet links may have limited metadata availability
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Loading Indicator -->
                        <div id="analysisLoading" style="display: none;" class="text-center mb-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Analyzing...</span>
                            </div>
                            <p class="mt-2">Analyzing torrent and searching for matches...</p>
                        </div>

                        <!-- Results Container -->
                        <div id="matchResults" class="mb-4" style="display: none;">
                            <h5>Potential Matches</h5>
                            <div id="matchResultsContent" class="list-group">
                                <!-- Matches will be inserted here -->
                            </div>
                            <button type="button" class="btn btn-secondary mt-3 w-100"
                                    onclick="switchToManualImport()">
                                None of these match? Import manually
                            </button>
                        </div>
                    </div>
                </div>


                <!-- Torrent Upload Parameters Section -->
                <div id="torrentUploadParameters" style="display: none;">
                    <div class="section-container">
                        <!-- Basic Content Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Basic Information</h5>
                            </div>
                            <div class="card-body">
                                <!-- Content Type and Release Date -->
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label for="metaType" class="form-label">Content Type</label>
                                        <select class="form-select" id="metaType" name="metaType" onchange="updateContentType()">
                                            <option value="movie" {% if prefill_data.meta_type == 'movie' %}selected{% endif %}>Movie</option>
                                            <option value="series" {% if prefill_data.meta_type == 'series' %}selected{% endif %}>Series</option>
                                            <option value="sports">Sports</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="torrentType" class="form-label">Torrent Type</label>
                                        <select class="form-select" id="torrentType" name="torrentType">
                                            <option value="public">Public</option>
                                            <option value="semi-private">Semi-Private</option>
                                            <option value="private">Private</option>
                                            <option value="web-seed">Web-Seed</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="createdAt" class="form-label">Release Date (DD/MM/YYYY)</label>
                                        <input type="text"
                                               class="form-control"
                                               id="createdAt"
                                               name="createdAt"
                                               required
                                               placeholder="dd/mm/yyyy">
                                    </div>
                                </div>

                                <!-- Content Metadata -->
                                <div class="row mb-3">
                                    <div class="col-md-4" id="metaIdContainer">
                                        <label for="torrentImdbId" class="form-label">IMDb ID</label>
                                        <input type="text" class="form-control" id="torrentImdbId" maxlength="20" placeholder="e.g., tt1234567"
                                               value="{% if prefill_data.meta_id %}{{ prefill_data.meta_id }}{% endif %}">
                                    </div>
                                    <div class="col-md-8">
                                        <label for="title" class="form-label">Title</label>
                                        <alert class="alert alert-info py-1" role="alert">
                                            <i class="bi bi-info circle"></i> Validate auto-filled data before submission
                                        </alert>
                                        <input type="text" class="form-control" id="title" placeholder="Content title">
                                    </div>
                                </div>

                                <!-- Media URLs -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="poster" class="form-label">Poster URL</label>
                                        <input type="url" class="form-control" id="poster">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="background" class="form-label">Background URL</label>
                                        <input type="url" class="form-control" id="background">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="logo" class="form-label">Logo URL</label>
                                        <input type="url" class="form-control" id="logo">
                                    </div>
                                </div>

                                <!-- Add title to poster option -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="addTitleToPoster" name="addTitleToPoster">
                                        <label class="form-check-label" for="addTitleToPoster">
                                            Add title to poster
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Technical Specifications -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Technical Specifications</h5>
                            </div>
                            <div class="card-body">
                                <div class="technical-specs-basic"></div>
                            </div>
                        </div>

                        <!-- Catalogs Section -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Content Categories</h5>
                            </div>
                            <div class="card-body">
                                <!-- Movie Catalogs -->
                                <div id="movieCatalogs" class="catalog-group" style="display: none;">
                                    <div class="alert alert-info" role="alert">
                                        <i class="bi bi-info-circle"></i> Select categories to help organize your content
                                    </div>
                                    <div class="catalog-grid">
                                        {% for catalog in supported_movie_catalog_ids %}
                                            <div class="catalog-item">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="catalogs"
                                                           value="{{ catalog }}" id="catalog-{{ catalog }}">
                                                    <label class="form-check-label" for="catalog-{{ catalog }}">
                                                        {{ catalog_data.get(catalog) }}
                                                    </label>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>

                                <!-- Series Catalogs -->
                                <div id="seriesCatalogs" class="catalog-group" style="display: none;">
                                    <div class="alert alert-info" role="alert">
                                        <i class="bi bi-info-circle"></i> Select categories to help organize your content
                                    </div>
                                    <div class="catalog-grid">
                                        {% for catalog in supported_series_catalog_ids %}
                                            <div class="catalog-item">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="catalogs"
                                                           value="{{ catalog }}" id="catalog-{{ catalog }}">
                                                    <label class="form-check-label" for="catalog-{{ catalog }}">
                                                        {{ catalog_data.get(catalog) }}
                                                    </label>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>

                                <!-- Sports Catalogs -->
                                <div id="sportsCatalogs" class="catalog-group" style="display: none;">
                                    <div class="col-md-12">
                                        <select class="form-select" id="sportsCatalog">
                                            <option value="">Select Sports Category</option>
                                            <option value="american_football">American Football / NFL</option>
                                            <option value="baseball">Baseball / MLB</option>
                                            <option value="basketball">Basketball / NBA</option>
                                            <option value="football">Football / Soccer</option>
                                            <option value="formula_racing">Formula Racing</option>
                                            <option value="hockey">Hockey / NHL</option>
                                            <option value="motogp_racing">MotoGP Racing</option>
                                            <option value="other_sports">Other Sports</option>
                                            <option value="rugby">Rugby / AFL</option>
                                            <option value="wwe">WWE</option>
                                            <option value="ufc">UFC</option>
                                            <option value="fighting">Fighting</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Torrent Data Section -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Torrent Data</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="torrentFile" class="form-label">Torrent File</label>
                                    <input type="file" class="form-control" id="torrentFile" accept=".torrent" onchange="toggleInput('magnetLink', this)">
                                </div>
                                <div class="mb-3">
                                    <label for="magnetLink" class="form-label">Magnet Link</label>
                                    <input type="text" class="form-control" id="magnetLink" placeholder="magnet:?xt=urn:btih:..." onchange="toggleInput('torrentFile', this)">
                                </div>
                            </div>
                        </div>

                        <!-- Uploader Information -->
                        <div class="mb-3">
                            <label for="uploaderName" class="form-label">Uploader Name (Optional)</label>
                            <input type="text" class="form-control" id="uploaderName" placeholder="Enter your name or leave blank for Anonymous">
                        </div>
                    </div>

                    <!-- Success Alert with Import New Button -->
                    <div class="alert alert-success alert-dismissible fade" role="alert" id="successAlert">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <span id="successMessage">Torrent added successfully!</span>
                            </div>
                            <button type="button" class="btn btn-success btn-sm" onclick="switchToQuickImport()">
                                Import New Torrent
                            </button>
                        </div>
                        <button type="button" class="btn-close" onclick="hideSuccessAlert()"></button>
                    </div>

                </div>

                <!-- Scrapy Parameters -->
                <div id="scrapyParameters" style="display: none;">
                    <div class="section-container">
                        <div class="mb-3">
                            <label for="spiderName" class="form-label">Spider Name</label>
                            <select class="form-select" id="spiderName" name="spider_name">
                                {% for spider_id, spider_name in scrapy_spiders %}
                                    <option value="{{ spider_id }}">{{ spider_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <!-- Additional Parameters based on Spider Selection -->
                        <div id="tamilmvTamilblastersParams" style="display: none;" class="mb-3 mt-4">
                            <label><input type="radio" name="mode" value="page_scraping" onchange="toggleModeSpecificFields()"> Page Scraping</label>
                            <label><input type="radio" name="mode" value="keyword_search" checked onchange="toggleModeSpecificFields()"> Keyword Search</label>

                            <!-- Keyword Search Input -->
                            <div id="keywordSearchInput" class="mb-3" style="display: none;">
                                <label for="keyword" class="form-label">Search Keyword</label>
                                <input type="text" class="form-control" id="keyword" name="search_keyword">
                            </div>

                            <!-- Page Scraping Input -->
                            <div id="pageScrapingInput" class="mb-3" style="display: none;">
                                <label for="pages" class="form-label">Number of Pages</label>
                                <input type="number" class="form-control" id="pages" name="pages" value="1">
                                <label for="startPage" class="form-label">Start Page</label>
                                <input type="number" class="form-control" id="startPage" name="start_page" value="1">

                                <!-- Scrap Catalog ID Dropdown -->
                                <label for="scrapCatalogId" class="form-label">Scrap Catalog ID</label>
                                <select class="form-select" id="scrapCatalogId" name="scrap_catalog_id">
                                    <option value="all">All</option>
                                    <option value="tamil_hdrip">Tamil HDRip</option>
                                    <option value="tamil_tcrip">Tamil TCrip</option>
                                    <option value="tamil_dubbed">Tamil Dubbed</option>
                                    <option value="tamil_series">Tamil Series</option>
                                    <option value="malayalam_hdrip">Malayalam HDRip</option>
                                    <option value="malayalam_tcrip">Malayalam TCrip</option>
                                    <option value="malayalam_dubbed">Malayalam Dubbed</option>
                                    <option value="malayalam_series">Malayalam Series</option>
                                    <option value="telugu_tcrip">Telugu TCrip</option>
                                    <option value="telugu_hdrip">Telugu HDRip</option>
                                    <option value="telugu_dubbed">Telugu Dubbed</option>
                                    <option value="telugu_series">Telugu Series</option>
                                    <option value="hindi_tcrip">Hindi TCrip</option>
                                    <option value="hindi_hdrip">Hindi HDRip</option>
                                    <option value="hindi_dubbed">Hindi Dubbed</option>
                                    <option value="hindi_series">Hindi Series</option>
                                    <option value="kannada_tcrip">Kannada TCrip</option>
                                    <option value="kannada_hdrip">Kannada HDRip</option>
                                    <option value="kannada_series">Kannada Series</option>
                                    <option value="english_tcrip">English TCrip</option>
                                    <option value="english_hdrip">English HDRip</option>
                                    <option value="english_series">English Series</option>
                                </select>
                            </div>
                        </div>
                        <div id="scrapeAllOption" class="mb-3" style="display: none;">
                            <label for="totalPages" class="form-label">Total Number of Pages</label>
                            <input type="number" class="form-control" id="totalPages" name="total_pages" value="1">
                            <label><input type="checkbox" name="scrape_all" id="scrape_all"> Scrape All Pages</label>
                        </div>

                    </div>
                </div>

                <!-- TV Metadata Input -->
                <div id="tvMetadataInput" style="display: none;">
                    <div class="section-container">
                        <h4 class="section-header">TV Metadata Input</h4>
                        <hr class="section-divider">
                        <div class="mb-3">
                            <label for="title" class="form-label">Title</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="poster" class="form-label">Poster URL (Optional)</label>
                            <input type="url" class="form-control" id="poster" name="poster">
                        </div>
                        <div class="mb-3">
                            <label for="logo" class="form-label">Logo URL (Optional)</label>
                            <input type="url" class="form-control" id="logo" name="logo">
                        </div>
                        <div class="mb-3">
                            <label for="background" class="form-label">Background URL (Optional)</label>
                            <input type="url" class="form-control" id="background" name="background">
                        </div>
                        <div class="mb-3">
                            <label for="country" class="form-label">Country (Optional)</label>
                            <input type="text" class="form-control" id="country" name="country">
                        </div>
                        <div class="mb-3">
                            <label for="language" class="form-label">Language (Optional)</label>
                            <input type="text" class="form-control" id="language" name="language">
                        </div>
                        <div class="mb-3">
                            <label for="genreInput" class="form-label">Genres (Optional)</label>
                            <div id="genreInputs" class="mb-2"></div>
                            <input type="text" class="form-control mb-2" id="genreInput" placeholder="Add genre">
                            <button type="button" class="btn btn-secondary" onclick="addGenre()">Add Genre</button>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Streams</label>
                            <div id="streamInputs"></div>
                            <button type="button" class="btn btn-primary" onclick="addStreamInput()">Add Stream</button>
                        </div>

                    </div>
                </div>

                <div id="m3uPlaylistInput" style="display: none;">
                    <div class="section-container">

                        <div class="mb-3">
                            <div>
                                <label><input type="radio" name="m3uInputType" value="url" checked> M3U Playlist URL</label>
                                <label><input type="radio" name="m3uInputType" value="file"> Upload M3U File</label>
                            </div>
                        </div>
                        <div class="mb-3" id="m3uPlaylistUrlInput">
                            <label for="m3uPlaylistUrl" class="form-label">M3U Playlist URL</label>
                            <input type="url" class="form-control" id="m3uPlaylistUrl" name="m3uPlaylistUrl">
                        </div>
                        <div class="mb-3" id="m3uPlaylistFileInput" style="display: none;">
                            <label for="m3uPlaylistFile" class="form-label">Upload M3U File</label>
                            <input type="file" class="form-control" id="m3uPlaylistFile" name="m3uPlaylistFile" accept=".m3u">
                        </div>
                        <div class="mb-3">
                            <label for="m3uPlaylistSource" class="form-label">Playlist Source Name</label>
                            <input type="text" class="form-control" id="m3uPlaylistSource" name="m3uPlaylistSource">
                        </div>

                    </div>
                </div>

                <!-- IMDb Data Update Parameters -->
                <div id="imdbDataParameters" style="display: none;">
                    <div class="section-container">

                        <div class="mb-3">
                            <label for="imdbId" class="form-label">IMDb ID</label>
                            <input type="text" class="form-control" id="imdbId" name="imdb_id" placeholder="Enter IMDb ID (e.g., tt1234567)">
                        </div>
                        <div class="mb-3">
                            <label for="imdbType" class="form-label">IMDb Type</label>
                            <select class="form-select" id="imdbType" name="imdb_type">
                                <option value="movie">Movie</option>
                                <option value="series">Series</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div id="blockTorrentParameters" style="display: none;">
                    <div class="section-container">

                        <div class="mb-3">
                            <label for="blockTorrentInfoHash" class="form-label">Torrent Info Hash</label>
                            <input type="text" class="form-control" id="blockTorrentInfoHash" name="blockTorrentInfoHash" required
                                   value="{% if prefill_data.info_hash %}{{ prefill_data.info_hash }}{% endif %}">
                        </div>
                        <div class="mb-3">
                            <label for="blockTorrentAction" class="form-label">Action</label>
                            <select class="form-select" id="blockTorrentAction" name="blockTorrentAction" required>
                                <option value="block">Block</option>
                                <option value="delete">Delete</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div id="migrationParameters" style="display: none;">
                    <div class="section-container">

                        <div class="mb-3">
                            <label for="mediafusionId" class="form-label">MediaFusion ID</label>
                            <input type="text" class="form-control" id="mediafusionId" name="mediafusion_id" required
                                   value="{% if prefill_data.mediafusion_id %}{{ prefill_data.mediafusion_id }}{% endif %}">
                        </div>
                        <div class="mb-3">
                            <label for="migrationImdbId" class="form-label">IMDb ID</label>
                            <input type="text" class="form-control" id="migrationImdbId" name="imdb_id"
                                   placeholder="Enter IMDb ID (e.g., tt1234567)" required>
                        </div>
                        <div class="mb-3">
                            <label for="mediaType" class="form-label">Media Type</label>
                            <select class="form-select" id="mediaType" name="media_type">
                                <option value="movie" {% if prefill_data.meta_type == 'movie' %}selected{% endif %}>Movie</option>
                                <option value="series" {% if prefill_data.meta_type == 'series' %}selected{% endif %}>Series</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div id="updateImagesParameters" style="display: none;">
                    <div class="section-container">
                        <h4 class="section-header">Update Images</h4>
                        <hr class="section-divider">

                        <div class="alert alert-info" role="alert">
                            <i class="bi bi-info-circle"></i>
                            Provide the content ID and at least one image URL to update
                        </div>

                        <!-- Content ID Input -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Content Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="imageUpdateMetaId" class="form-label">Content ID *</label>
                                    <input type="text"
                                           class="form-control"
                                           id="imageUpdateMetaId"
                                           name="meta_id"
                                           required
                                           placeholder="Enter IMDb ID (tt1234567) or MediaFusion ID">
                                    <div class="form-text">
                                        <i class="bi bi-info-circle-fill"></i>
                                        This can be either an IMDb ID (starts with 'tt') or a MediaFusion ID (starts with 'mf')
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Image URLs Card -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Image URLs</h5>
                                <small class="text-muted">At least one image URL must be provided</small>
                            </div>
                            <div class="card-body">
                                <!-- Poster URL -->
                                <div class="mb-4">
                                    <label for="imageUpdatePoster" class="form-label">Poster URL</label>
                                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-image"></i>
                        </span>
                                        <input type="url"
                                               class="form-control"
                                               id="imageUpdatePoster"
                                               name="poster"
                                               placeholder="https://example.com/poster.jpg">
                                    </div>
                                    <div class="form-text">High-quality vertical image for the content poster</div>
                                </div>

                                <!-- Background URL -->
                                <div class="mb-4">
                                    <label for="imageUpdateBackground" class="form-label">Background URL</label>
                                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-image-fill"></i>
                        </span>
                                        <input type="url"
                                               class="form-control"
                                               id="imageUpdateBackground"
                                               name="background"
                                               placeholder="https://example.com/background.jpg">
                                    </div>
                                    <div class="form-text">Wide image to be used as background/backdrop</div>
                                </div>

                                <!-- Logo URL -->
                                <div class="mb-3">
                                    <label for="imageUpdateLogo" class="form-label">Logo URL</label>
                                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-vector-pen"></i>
                        </span>
                                        <input type="url"
                                               class="form-control"
                                               id="imageUpdateLogo"
                                               name="logo"
                                               placeholder="https://example.com/logo.png">
                                    </div>
                                    <div class="form-text">Transparent PNG logo of the content</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- API Password Configuration -->
            <input type="hidden" id="apiPasswordEnabled" value="{{ api_password_enabled }}">
            <div id="apiPasswordContainer" class="section-container" style="display: none;">
                <h4 class="section-header">API Security Configuration</h4>
                <hr class="section-divider">
                <div class="mb-3">
                    <label for="api_password">API Password:</label>
                    <div class="input-group">
                        <input class="form-control" type="password" id="api_password" name="api_password" placeholder="Enter API Password">
                        <button class="btn btn-outline-secondary" type="button" id="toggleApiPassword">
                            <span id="toggleApiPasswordIcon" class="bi bi-eye"></span>
                        </button>
                    </div>
                </div>
            </div>

            <div class="button-container mt-5">
                <button type="button" class="btn btn-primary" id="submitBtn" onclick="submitScraperForm()">Run</button>
                <div id="loadingSpinner" class="spinner-border text-primary" role="status" style="display: none;">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File Annotation Modal -->
<div class="modal fade" id="fileAnnotationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Annotate Video Files</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> Please specify season and episode numbers for each video file
                </div>

                <!-- File list for annotation -->
                <div id="fileAnnotationList"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmAnnotation">Confirm</button>
            </div>
        </div>
    </div>
</div>


<!-- Spec Selection Modal -->
<div class="modal fade" id="specSelectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="specModalTitle">Select Specification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Single Selection -->
                <div id="singleSpecSelection" style="display: none;">
                    <select class="form-select mb-3" id="specSelectSingle">
                        <!-- Options will be populated dynamically -->
                    </select>
                </div>

                <!-- Multiple Selection -->
                <div id="multipleSpecSelection" style="display: none;">
                    <div class="spec-checkboxes mb-3">
                        <!-- Checkboxes will be populated dynamically -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmSpecSelection">Apply</button>
            </div>
        </div>
    </div>
</div>

<input type="hidden" id="supportedLanguages" value='{{ supported_languages|tojson|safe }}'>

<!-- JS for Bootstrap -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/flatpickr/4.6.13/flatpickr.min.js"></script>
<script src="/static/js/scraperControl.js"></script>
</body>

</html>
