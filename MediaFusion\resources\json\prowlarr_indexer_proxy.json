{"onHealthIssue": false, "supportsOnHealthIssue": false, "includeHealthWarnings": false, "name": "<PERSON>lareSol<PERSON>r", "fields": [{"name": "host", "value": "$FLARESOLVERR_HOST"}, {"name": "requestTimeout", "value": 60}], "implementationName": "<PERSON>lareSol<PERSON>r", "implementation": "<PERSON>lareSol<PERSON>r", "configContract": "FlareSolverrSettings", "infoLink": "https://wiki.servarr.com/prowlarr/supported#flaresolverr", "tags": [1]}