{
  "id": "stremio.addons.{{ addon_name | lower | replace(' ', '') }}{% if streaming_provider_name %}.{{ streaming_provider_name }}{% endif %}",
  "version": "{{ version }}",
  "name": "{{ addon_name }}",
  "contactEmail": "{{ contact_email }}",
  "description": "{{ description }}",
  "logo": "{{ logo_url }}",
  "behaviorHints": {
    "configurable": true,
    "configurationRequired": false
  },
  "resources": [
    {% if enable_catalogs or enable_watchlist_catalogs %}"catalog",{% endif %}
    {
      "name": "stream",
      "types": ["movie", "series", "tv", "events"],
      "idPrefixes": ["tt", "mf", "dl"]
    },
    {
      "name": "meta",
      "types": ["movie", "series", "tv", "events"],
      "idPrefixes": [{% if enable_imdb_metadata %}"tt", {% endif %}"mf", "dl"]
    }
  ],
  "types": ["movie", "series", "tv", "events"],
  "catalogs": [
    {% set comma = joiner(",") %}
    {% if enable_watchlist_catalogs %}
    {{ comma() }}
    {
      "id": "{{ streaming_provider_name }}_watchlist_movies",
      "name": "{{ streaming_provider_short_name }} Watchlist",
      "type": "movie",
      "extra": [{"name": "skip", "isRequired": false}]
    }
    {{ comma() }}
    {
      "id": "{{ streaming_provider_name }}_watchlist_series",
      "name": "{{ streaming_provider_short_name }} Watchlist",
      "type": "series",
      "extra": [{"name": "skip", "isRequired": false}]
    }
    {% endif %}
    {% if enable_catalogs %}
    {% set catalog_definitions = {
      "mediafusion_search_movies": {"type": "movie", "name": "Movies", "genres": [], "is_search": true},
      "mediafusion_search_series": {"type": "series", "name": "Series", "genres": [], "is_search": true},
      "mediafusion_search_tv": {"type": "tv", "name": "Live TV", "genres": [], "is_search": true},
      "tamil_hdrip": {"type": "movie", "name": "Tamil HD Movies"},
      "tamil_tcrip": {"type": "movie", "name": "Tamil TCRip Movies"},
      "tamil_old": {"type": "movie", "name": "Tamil Old Movies"},
      "tamil_dubbed": {"type": "movie", "name": "Tamil Dubbed Movies"},
      "tamil_series": {"type": "series", "name": "Tamil Series"},
      "malayalam_tcrip": {"type": "movie", "name": "Malayalam TCRip Movies"},
      "malayalam_hdrip": {"type": "movie", "name": "Malayalam HD Movies"},
      "malayalam_old": {"type": "movie", "name": "Malayalam Old Movies"},
      "malayalam_dubbed": {"type": "movie", "name": "Malayalam Dubbed Movies"},
      "malayalam_series": {"type": "series", "name": "Malayalam Series"},
      "telugu_tcrip": {"type": "movie", "name": "Telugu TCRip Movies"},
      "telugu_hdrip": {"type": "movie", "name": "Telugu HD Movies"},
      "telugu_old": {"type": "movie", "name": "Telugu Old Movies"},
      "telugu_dubbed": {"type": "movie", "name": "Telugu Dubbed Movies"},
      "telugu_series": {"type": "series", "name": "Telugu Series"},
      "hindi_tcrip": {"type": "movie", "name": "Hindi TCRip Movies"},
      "hindi_hdrip": {"type": "movie", "name": "Hindi HD Movies"},
      "hindi_old": {"type": "movie", "name": "Hindi Old Movies"},
      "hindi_dubbed": {"type": "movie", "name": "Hindi Dubbed Movies"},
      "hindi_series": {"type": "series", "name": "Hindi Series"},
      "kannada_tcrip": {"type": "movie", "name": "Kannada TCRip Movies"},
      "kannada_hdrip": {"type": "movie", "name": "Kannada HD Movies"},
      "kannada_old": {"type": "movie", "name": "Kannada Old Movies"},
      "kannada_dubbed": {"type": "movie", "name": "Kannada Dubbed Movies"},
      "kannada_series": {"type": "series", "name": "Kannada Series"},
      "english_hdrip": {"type": "movie", "name": "English HD Movies"},
      "english_tcrip": {"type": "movie", "name": "English TCRip Movies"},
      "english_series": {"type": "series", "name": "English Series"},
      "bangla_movies": {"type": "series", "name": "Bangla Movies"},
      "bangla_series": {"type": "movie", "name": "Bangla Series"},
      "punjabi_movies": {"type": "movie", "name": "Punjabi Movies"},
      "punjabi_series": {"type": "series", "name": "Punjabi Series"},
      "live_tv": {"type": "tv", "name": "Live TV"},
      "formula_racing": {"type": "series", "name": "Formula Racing", "genres": []},
      "motogp_racing": {"type": "series", "name": "MotoGP Racing", "genres": []},
      "american_football": {"type": "movie", "name": "American Football", "genres": []},
      "basketball": {"type": "movie", "name": "Basketball", "genres": []},
      "baseball": {"type": "movie", "name": "Baseball", "genres": []},
      "football": {"type": "movie", "name": "Football", "genres": []},
      "hockey": {"type": "movie", "name": "Hockey", "genres": []},
      "rugby": {"type": "movie", "name": "Rugby/AFL", "genres": []},
      "fighting": {"type": "movie", "name": "Fighting (WWE, UFC)", "genres": ["WWE", "UFC"]},
      "tgx_movie": {"type": "movie", "name": "TGx Movies"},
      "tgx_series": {"type": "series", "name": "TGx Series"},
      "contribution_movies": {"type": "movie", "name": "Contribution Movies"},
      "contribution_series": {"type": "series", "name": "Contribution Series"},
      "other_sports": {"type": "movie", "name": "Other Sports", "genres": []},
      "live_sport_events": {"type": "events", "name": "Live Sport Events", "genres": ["American Football", "Athletics", "Aussie Rules", "Baseball", "Basketball", "Bowling", "Boxing", "Cricket", "Cycling", "Dart", "Floorball", "Football", "Futsal", "GAA", "Golf", "Gymnastics", "Handball", "Hockey", "Horse Racing", "Lacrosse", "MMA", "Motor Sport", "Netball", "Other Sports", "Rugby/AFL", "Squash", "Tennis", "Volleyball"]},
      "prowlarr_movies": {"type": "movie", "name": "Prowlarr Scraped Movies"},
      "prowlarr_series": {"type": "series", "name": "Prowlarr Scraped Series"},
      "arabic_movies": {"type": "movie", "name": "Arabic Movies"},
      "arabic_series": {"type": "series", "name": "Arabic Series"}
    } %}
    {% for catalog_id in selected_catalogs %}
    {% set catalog = catalog_definitions[catalog_id] | default(mdblist_data[catalog_id]) %}
	{% if "mdblist" in catalog_id %}
	{{ comma() }}
	{
	  "id": "{{ catalog_id }}",
	  "type": "{{ catalog.catalog_type }}",
	  "name": "{{ catalog.title }}",
	  "extra": [
		{"name": "skip", "isRequired": false},
		{"name": "genre", "isRequired": false, "options": ["action","anime","comedy","crime","documentary","drama","family","fantasy","history","holiday","horror","music","musical","mystery","science-fiction","short","sporting-event","superhero","suspense","thriller","war","western","animation","adventure","romance","reality","soap","news","talk-show","biography","sci-fi","sport","film-noir","reality-tv","game-show","special-interest","children","home-and-garden","tv-movie","sports","eastern","disaster","donghua","sci-fi-fantasy","action-adventure","talk","war-politics","kids"]}
	  ]
	}
    {% elif catalog %}
    {% set genres = catalog.genres | default(genres[catalog.type]) | default([]) %}
    {{ comma() }}
    {
      "id": "{{ catalog_id }}",
      "type": "{{ catalog.type }}",
      "name": "{{ catalog.name }}",
      "extra": [
		{% if catalog.is_search %}
		{"name": "search", "isRequired": true}
		{% else %}
        {"name": "skip", "isRequired": false}
		{% endif %}
        {% if genres %}
        , {"name": "genre", "isRequired": false, "options": {{ genres | tojson }}}
        {% endif %}
      ]
    }
    {% endif %}
    {% endfor %}
    {% endif %}
  ]
}