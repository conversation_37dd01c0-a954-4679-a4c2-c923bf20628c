@echo off
echo 🚀 Starting MediaFusion...
echo.

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  Warning: .env file not found!
    echo 📝 Please create a .env file with the required configuration.
    echo 📖 See the documentation for details.
    pause
    exit /b 1
)

echo 🌐 Starting MediaFusion server...
echo ⏹️  Press Ctrl+C to stop the server
echo.

REM Start the server using Python
python start.py

pause
