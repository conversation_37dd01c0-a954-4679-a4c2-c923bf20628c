#!/usr/bin/env python3
"""
MediaFusion Startup Script
Simple script to start the MediaFusion server with uvicorn
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("🚀 Starting MediaFusion...")
    print(f"📁 Working directory: {script_dir}")
    
    # Check if .env file exists
    env_file = script_dir / ".env"
    if not env_file.exists():
        print("⚠️  Warning: .env file not found!")
        print("📝 Please create a .env file with the required configuration.")
        print("📖 See the .env.example or documentation for details.")
        return 1
    
    # Default settings
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    
    print(f"🌐 Server will start at: http://{host}:{port}")
    print("🔄 Starting with auto-reload enabled...")
    print("⏹️  Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Start uvicorn with the FastAPI app
        cmd = [
            sys.executable, "-m", "uvicorn",
            "api.main:app",
            "--host", host,
            "--port", str(port),
            "--reload",
            "--log-level", "info"
        ]
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 MediaFusion server stopped.")
        return 0
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
