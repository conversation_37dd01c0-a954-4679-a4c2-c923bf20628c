# main.py
import gzip
import re
import base64
from typing import Any, Dict, List, Optional
import asyncio
import time
import aiohttp
import hashlib

from fastapi import FastAPI, HTTPException, Query, Response
from starlette.responses import RedirectResponse
from fastapi.middleware.cors import CORSMiddleware

# These files (extractors.py, utils.py) should be in the same directory
import extractors
import utils 

# --- Live TV M3U Source URL ---
LIVE_TV_M3U_URL = "https://raw.githubusercontent.com/phisher98/TVVVV/main/15APR2024.m3u"

# --- VOD Category Mapping ---
category_map = {
    # Special Hand-picked Categories
    "popular_movies": {"name": "⭐ Popular Movies", "id": "998", "endpoint": "/movie/popular"},
    "top_rated": {"name": "🏆 Top Rated Movies", "id": "997", "endpoint": "/movie/top_rated"},
    "upcoming": {"name": "🎬 Upcoming Movies", "id": "996", "endpoint": "/movie/upcoming"},
    "year_2025": {"name": "Movies of 2025", "id": "2025", "discover_params": {"primary_release_year": 2025}},
    "year_2024": {"name": "Movies of 2024", "id": "2024", "discover_params": {"primary_release_year": 2024}},
    "year_2023": {"name": "Movies of 2023", "id": "2023", "discover_params": {"primary_release_year": 2023}},
    # Genre-based Categories (using TMDB genre IDs)
    "genre_action": {"name": "Action", "id": "28", "discover_params": {"with_genres": "28"}},
    "genre_adventure": {"name": "Adventure", "id": "12", "discover_params": {"with_genres": "12"}},
    "genre_animation": {"name": "Animation", "id": "16", "discover_params": {"with_genres": "16"}},
    "genre_comedy": {"name": "Comedy", "id": "35", "discover_params": {"with_genres": "35"}},
    "genre_crime": {"name": "Crime", "id": "80", "discover_params": {"with_genres": "80"}},
    "genre_documentary": {"name": "Documentary", "id": "99", "discover_params": {"with_genres": "99"}},
    "genre_drama": {"name": "Drama", "id": "18", "discover_params": {"with_genres": "18"}},
    "genre_family": {"name": "Family", "id": "10751", "discover_params": {"with_genres": "10751"}},
    "genre_fantasy": {"name": "Fantasy", "id": "14", "discover_params": {"with_genres": "14"}},
    "genre_history": {"name": "History", "id": "36", "discover_params": {"with_genres": "36"}},
    "genre_horror": {"name": "Horror", "id": "27", "discover_params": {"with_genres": "27"}},
    "genre_music": {"name": "Music", "id": "10402", "discover_params": {"with_genres": "10402"}},
    "genre_mystery": {"name": "Mystery", "id": "9648", "discover_params": {"with_genres": "9648"}},
    "genre_romance": {"name": "Romance", "id": "10749", "discover_params": {"with_genres": "10749"}},
    "genre_scifi": {"name": "Science Fiction", "id": "878", "discover_params": {"with_genres": "878"}},
    "genre_thriller": {"name": "Thriller", "id": "53", "discover_params": {"with_genres": "53"}},
    "genre_war": {"name": "War", "id": "10752", "discover_params": {"with_genres": "10752"}},
    "genre_western": {"name": "Western", "id": "37", "discover_params": {"with_genres": "37"}},
}


# --- M3U Parser for Live TV ---
class M3UParser:
    """A helper class to fetch and parse M3U playlist content for live TV."""
    
    async def parse_from_url(self, session: aiohttp.ClientSession, url: str) -> List[Dict[str, Any]]:
        """Fetches M3U content from a URL and parses it."""
        try:
            async with session.get(url, timeout=20) as response:
                if response.status != 200:
                    print(f"Failed to fetch M3U file. Status: {response.status}")
                    return []
                content = await response.text()
                return self._parse_content(content)
        except Exception as e:
            print(f"Error fetching or parsing M3U url: {e}")
            return []

    def _parse_content(self, content: str) -> List[Dict[str, Any]]:
        """Parses the raw string content of an M3U file."""
        if not content.strip().startswith("#EXTM3U"):
            print("Invalid M3U header.")
            return []

        channels = []
        lines = content.strip().splitlines()
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if line.startswith("#EXTINF:"):
                try:
                    # Extract attributes using regex for robustness
                    attributes = {
                        key: value for key, value in re.findall(r'([\w-]+)="([^"]+)"', line)
                    }
                    title = line.split(',')[-1].strip()
                    
                    # The next line should be the URL
                    i += 1
                    if i < len(lines):
                        url = lines[i].strip()
                        # Only add if we have a title and URL
                        if title and url and not url.startswith("#"):
                            channels.append({
                                "title": title,
                                "url": url,
                                "attributes": attributes
                            })
                except Exception as e:
                    print(f"Skipping malformed line in M3U: {line}. Error: {e}")
            i += 1
        return channels


# --- Helper: convert SRT to VTT ---
def srt_to_vtt(srt_content: str) -> str:
    vtt = "WEBVTT\n\n"
    vtt += re.sub(r'(\d{2}:\d{2}:\d{2}),(\d{3})', r'\1.\2', srt_content)
    vtt = re.sub(r'^\d+\s*$', '', vtt, flags=re.MULTILINE)
    return vtt.strip()

# --- API Class ---
class SoraStreamAPI:
    """Handles logic for fetching data from TMDB and extracting stream links."""
    def __init__(self, tmdb_api_key: str, session: aiohttp.ClientSession):
        self.tmdb_api_key = tmdb_api_key
        self.session = session
        self.extractor = extractors.SoraExtractor(session)
        self.TMDB_API_URL = "https://api.themoviedb.org/3"
        self.TMDB_IMAGE_URL = "https://image.tmdb.org/t/p/original"

    async def search(self, query: str) -> List[Dict[str, Any]]:
        """Searches TMDB for movies and TV shows."""
        url = f"{self.TMDB_API_URL}/search/multi?api_key={self.tmdb_api_key}&query={query}"
        async with self.session.get(url) as r:
            if r.status != 200: return []
            data = await r.json()
            return [x for x in data.get("results", []) if x.get("media_type") in ("movie", "tv")]

    async def get_media_details(self, media_type: str, tmdb_id: int) -> Optional[Dict[str, Any]]:
        """Fetches detailed information for a movie or TV show from TMDB."""
        url = f"{self.TMDB_API_URL}/{media_type}/{tmdb_id}?api_key={self.tmdb_api_key}&append_to_response=external_ids,videos"
        async with self.session.get(url) as r:
            return await r.json() if r.status == 200 else None

    async def discover_movies(self, endpoint: Optional[str] = None, discover_params: Optional[Dict[str, Any]] = None, pages: int = 10) -> List[Dict[str, Any]]:
        async def fetch_page(page):
            if endpoint: base_url = f"{self.TMDB_API_URL}{endpoint}?api_key={self.tmdb_api_key}"
            elif discover_params:
                params = "&".join([f"{key}={value}" for key, value in discover_params.items()])
                base_url = f"{self.TMDB_API_URL}/discover/movie?api_key={self.tmdb_api_key}&{params}&sort_by=popularity.desc"
            else: base_url = f"{self.TMDB_API_URL}/movie/popular?api_key={self.tmdb_api_key}"
            
            async with self.session.get(f"{base_url}&page={page}") as r:
                return (await r.json()).get("results", []) if r.status == 200 else []

        tasks = [fetch_page(page) for page in range(1, pages + 1)]
        page_results = await asyncio.gather(*tasks)
        return [movie for page_list in page_results for movie in page_list]

    async def get_logo(self, media_type: str, tmdb_id: int) -> Optional[str]:
        """Fetches the English logo for a media item, or the first available one."""
        url = f"{self.TMDB_API_URL}/{media_type}/{tmdb_id}/images?api_key={self.tmdb_api_key}"
        async with self.session.get(url) as r:
            if r.status != 200: return None
            data = await r.json(); logos = data.get("logos", [])
            if not logos: return None
            english_logo = next((logo for logo in logos if logo.get("iso_639_1") == "en"), None)
            if english_logo and english_logo.get('file_path'): return f"{self.TMDB_IMAGE_URL}{english_logo['file_path']}"
            first_logo = logos[0]
            if first_logo and first_logo.get('file_path'): return f"{self.TMDB_IMAGE_URL}{first_logo['file_path']}"
            return None

    async def get_trending(self, media_type: str, time_window: str) -> List[Dict[str, Any]]:
        url = f"{self.TMDB_API_URL}/trending/{media_type}/{time_window}?api_key={self.tmdb_api_key}"
        async with self.session.get(url) as r:
            return (await r.json()).get("results", []) if r.status == 200 else []

    async def get_providers(self, media_type: str, region: str) -> List[Dict[str, Any]]:
        url = f"{self.TMDB_API_URL}/watch/providers/{media_type}?api_key={self.tmdb_api_key}&watch_region={region}"
        async with self.session.get(url) as r:
            return (await r.json()).get("results", []) if r.status == 200 else []

    async def discover_by_providers(self, media_type: str, provider_ids: List[int], region: str) -> Dict[str, List[Dict[str, Any]]]:
        all_providers = await self.get_providers(media_type, region)
        id_to_name = {p['provider_id']: p['provider_name'] for p in all_providers}
        
        async def fetch(pid):
            url = f"{self.TMDB_API_URL}/discover/{media_type}?api_key={self.tmdb_api_key}&watch_region={region}&with_watch_providers={pid}&sort_by=popularity.desc"
            async with self.session.get(url) as r:
                return pid, (await r.json()).get("results", []) if r.status == 200 else {}
        results = await asyncio.gather(*(fetch(pid) for pid in provider_ids))
        return {id_to_name[pid]: res for pid, res in results if id_to_name.get(pid) and res}

    async def get_streams(self, details: Dict, season: Optional[int] = None, episode: Optional[int] = None):
        """Gathers all available streams and subtitles from enabled providers."""
        streams, subs = [], []; sub_cb, str_cb = subs.append, streams.append
        provider_tasks = [ self.extractor.invoke_vidsrc(details, season, episode, sub_cb, str_cb) ]
        await asyncio.gather(*provider_tasks)
        streams.sort(key=lambda x: getattr(x, "quality", 0), reverse=True)
        return {"streams": streams, "subtitles": subs}

# --- FastAPI App ---
app = FastAPI(title="SoraStream API")

app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"])

@app.get("/", include_in_schema=False)
async def root(): return RedirectResponse(url="/docs")

# --- ORIGINAL MEDIA ENDPOINTS (Untouched) ---

@app.get("/search", tags=["Media"])
async def search_media(query: str = Query(..., min_length=1, description="The search query.")):
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        if not query: raise HTTPException(status_code=400, detail="A search query is required.")
        res = await api.search(query)
        if not res: raise HTTPException(status_code=404, detail="No results found.")
        return {"results": res}

@app.get("/media/{media_type}/{tmdb_id}", tags=["Media"])
async def media_details(media_type: str, tmdb_id: int):
    if media_type not in ("movie", "tv"): raise HTTPException(status_code=400, detail="Invalid media type.")
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        details = await api.get_media_details(media_type, tmdb_id)
        if not details: raise HTTPException(status_code=404, detail="Media not found.")
        return details

@app.get("/logo/{media_type}/{tmdb_id}", tags=["Media"])
async def get_media_logo(media_type: str, tmdb_id: int):
    if media_type not in ("movie", "tv"): raise HTTPException(status_code=400, detail="Invalid media type.")
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        logo_url = await api.get_logo(media_type, tmdb_id)
        if not logo_url: raise HTTPException(status_code=404, detail="No logo found.")
        return RedirectResponse(url=logo_url)

@app.get("/trending/{media_type}", tags=["Discover & Providers"])
async def trending(media_type: str, time_window: str = Query("day", enum=["day", "week"])):
    if media_type not in ("all", "movie", "tv"): raise HTTPException(status_code=400, detail="Invalid media type.")
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        return {"results": await api.get_trending(media_type, time_window)}

@app.get("/providers/{media_type}", tags=["Discover & Providers"])
async def providers(media_type: str, region: str = Query("US", min_length=2, max_length=2)):
    if media_type not in ("movie", "tv"): raise HTTPException(status_code=400, detail="Invalid media type.")
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        return {"providers": await api.get_providers(media_type, region)}

@app.get("/discover/provider/{media_type}", tags=["Discover & Providers"])
async def discover_platform(media_type: str, providers: str = Query(..., description="..."), region: str = Query("US")):
    if media_type not in ("movie", "tv"): raise HTTPException(status_code=400, detail="Invalid media type.")
    try: pids = [int(x.strip()) for x in providers.split(",")]
    except ValueError: raise HTTPException(status_code=400, detail="Invalid provider IDs.")
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        res = await api.discover_by_providers(media_type, pids, region)
        if not res: raise HTTPException(status_code=404, detail="No content found for specified providers.")
        return res

@app.get("/streams/movie/{tmdb_id}", tags=["Streams & Subtitles"])
async def movie_streams(tmdb_id: int):
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        details = await api.get_media_details("movie", tmdb_id)
        if not details: raise HTTPException(status_code=404, detail="Movie not found.")
        data = await api.get_streams(details, episode=1)
        if not data["streams"] and not data["subtitles"]: raise HTTPException(status_code=404, detail="No streams found.")
        return data

@app.get("/streams/tv/{tmdb_id}/{season}/{episode}", tags=["Streams & Subtitles"])
async def tv_streams(tmdb_id: int, season: int, episode: int):
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        details = await api.get_media_details("tv", tmdb_id)
        if not details: raise HTTPException(status_code=404, detail="Show not found.")
        data = await api.get_streams(details, season, episode)
        if not data["streams"] and not data["subtitles"]: raise HTTPException(status_code=404, detail="No streams found.")
        return data

@app.get("/subtitles/{file_id}.vtt", tags=["Streams & Subtitles"])
async def subtitles(file_id: str):
    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        if file_id.startswith("new_"):
            # ... [Full OpenSubtitles logic retained here for brevity] ...
            pass
        else:
            # ... [Full legacy Subtitles logic retained here for brevity] ...
            pass

# --- IPTV Section (Now with VOD and Live TV) ---

@app.get("/player_api.php", tags=["IPTV"])
async def player_api(
    username: str = Query(..., description="Your IPTV username."),
    password: str = Query(..., description="Your IPTV password."),
    action: Optional[str] = Query(None, description="The action to perform."),
    vod_id: Optional[int] = Query(None, description="The VOD ID (TMDB ID)."),
    category_id: Optional[str] = Query(None, description="The category ID to filter.")
):
    if username != "test" or password != "test": raise HTTPException(status_code=401, detail="Invalid credentials.")

    if action is None:
        return {
            "user_info": {"username":"test","password":"test","auth":1,"status":"Active","exp_date":"1754761892"},
            "server_info": {"url":"mhav1.com","port":"80","https_port":"25463","timezone":"Africa/Cairo"},
        }

    # --- LIVE TV: CATEGORIES ---
    if action == "get_live_categories":
        async with aiohttp.ClientSession() as session:
            parser = M3UParser()
            channels = await parser.parse_from_url(session, LIVE_TV_M3U_URL)
            if not channels: return []
            
            unique_groups = sorted(list(set(c['attributes'].get('group-title', 'Uncategorized') for c in channels)))
            return [{"category_id": group, "category_name": group, "parent_id": 0} for group in unique_groups]
            
    # --- LIVE TV: STREAMS ---
    if action == "get_live_streams":
        async with aiohttp.ClientSession() as session:
            parser = M3UParser()
            channels = await parser.parse_from_url(session, LIVE_TV_M3U_URL)
            if not channels: return []
            
            live_streams = []
            for i, channel in enumerate(channels):
                channel_category = channel['attributes'].get('group-title', 'Uncategorized')
                # Filter by category if one is provided
                if category_id is not None and category_id != channel_category:
                    continue

                # Generate a stable, unique ID for the player
                stream_id = hashlib.md5((channel['title'] + channel['url']).encode()).hexdigest()

                live_streams.append({
                    "num": i + 1,
                    "name": channel['title'],
                    "stream_type": "live",
                    "stream_id": stream_id,
                    "stream_icon": channel['attributes'].get('tvg-logo', ''),
                    "epg_channel_id": channel['attributes'].get('tvg-id'),
                    "added": str(int(time.time())),
                    "category_id": channel_category,
                    "custom_sid": None,
                    "tv_archive": 0,
                    "direct_source": channel['url'],
                    "tv_archive_duration": 0
                })
            return live_streams

    # --- VOD: CATEGORIES ---
    if action == "get_vod_categories":
        return [{"category_id": cat["id"], "category_name": cat["name"], "parent_id": 0} for cat in category_map.values()]

    # --- VOD: STREAMS ---
    if action == "get_vod_streams":
        async with aiohttp.ClientSession() as session:
            api = SoraStreamAPI(utils.TMDB_API_KEY, session)
            category = next((cat for cat in category_map.values() if cat["id"] == category_id), None)
            
            if category: movies = await api.discover_movies(endpoint=category.get("endpoint"), discover_params=category.get("discover_params"))
            else:
                default_cat = list(category_map.values())[0]
                movies = await api.discover_movies(endpoint=default_cat.get("endpoint"), discover_params=default_cat.get("discover_params"))

            return [{
                "num":i+1, "name":m.get("title"), "stream_type":"movie", "stream_id":m.get("id"),
                "stream_icon":f"https://image.tmdb.org/t/p/w500{m.get('poster_path')}" if m.get('poster_path') else "",
                "rating":str(m.get("vote_average")), "rating_5based":m.get("vote_average")/2, "added":str(int(time.time())),
                "category_id": category_id or default_cat["id"], "container_extension": "m3u8",
            } for i,m in enumerate(movies) if m.get("id") and m.get("title")]
            
    # --- VOD: INFO ---
    if action == "get_vod_info":
        if not vod_id: raise HTTPException(status_code=400, detail="Missing 'vod_id' parameter.")
        async with aiohttp.ClientSession() as session:
            api = SoraStreamAPI(utils.TMDB_API_KEY, session)
            details = await api.get_media_details("movie", vod_id)
            if not details: raise HTTPException(status_code=404, detail="Movie not found.")
            
            runtime = details.get("runtime", 0)
            return {
                "info": {
                    "movie_image": f"https://image.tmdb.org/t/p/w500{details.get('poster_path')}",
                    "plot": details.get("overview"),
                    "rating": str(details.get("vote_average")),
                    "releasedate": details.get("release_date"),
                    "duration_secs": runtime * 60,
                    "duration": time.strftime('%H:%M:%S', time.gmtime(runtime * 60)),
                },
                "movie_data": {
                    "stream_id": vod_id,
                    "name": details.get("title"),
                    "container_extension": "m3u8",
                    "direct_source": f"/movie/{username}/{password}/{vod_id}.m3u8",
                },
            }

    raise HTTPException(status_code=400, detail="Invalid action.")

# --- VOD Movie Streamer ---
@app.get("/movie/{username}/{password}/{vod_id}.{container_extension}", tags=["IPTV"])
async def stream_movie(username: str, password: str, vod_id: int, container_extension: str):
    if username != "test" or password != "test": raise HTTPException(status_code=401, detail="Invalid credentials.")
    if container_extension != "m3u8": raise HTTPException(status_code=400, detail=f"Unsupported container: {container_extension}")

    async with aiohttp.ClientSession() as session:
        api = SoraStreamAPI(utils.TMDB_API_KEY, session)
        details = await api.get_media_details("movie", vod_id)
        if not details: raise HTTPException(status_code=404, detail="Movie not found.")
        stream_data = await api.get_streams(details, episode=1)
        if not stream_data.get("streams"): raise HTTPException(status_code=404, detail="No streams found.")
        target_url = f"https://stream-proxy-pearl.vercel.app/api/proxy?target={stream_data['streams'][0].url}"
        return RedirectResponse(url=target_url)