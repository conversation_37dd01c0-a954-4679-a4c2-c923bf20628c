import requests

def search_mhiptv(query, offset=0, limit=50):
    url = "https://mhiptv.tech/api/search"
    headers = {
        "accept": "application/json",
        "content-type": "application/json"
    }
    data = {
        "offset": offset,
        "limit": limit,
        "q": query
    }

    response = requests.post(url, json=data, headers=headers)
    if response.ok:
        results = response.json().get("results", [])
        for item in results:
            print(f"{item.get('title')} - ID: {item.get('id')}")
    else:
        print(f"Error: {response.status_code}")

# Example usage
search_mhiptv("bein", offset=0)
